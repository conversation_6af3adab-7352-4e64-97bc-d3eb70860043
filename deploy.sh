set -e

NAME="kubernetes-demo-api"
USERNAME="tanvir909"
IMAGE="${USERNAME}/${NAME}:latest"


echo "Building Docker image: ${IMAGE}"
docker build -t ${IMAGE} .

echo "Pushing Docker Hub: ${IMAGE}"
docker push ${IMAGE}

echo "Applying Kubernetes manifests"
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml

echo "Waiting for pods to be ready"
kubectl get pods

echo "Getting services...."
kubectl get services

echo "Fetching the main service"
kubectl get service $NAME-service