url: https://www.youtube.com/watch?v=H5FAxTBuNM8&t=114s

- Kubernetes Demo - 02:03:35
	- create a repository in github
	- git clone the repository 

	- npm init -y
	- npm i express
	- installation kubectl - https://www.youtube.com/watch?v=G9MmLUsBd3g
	- installation minikube - https://www.youtube.com/watch?v=xNefZ51jHKg

	- config the .dockerignore, Dockerfile, docker-compose.yaml, index.js and .gitignore
	- docker compose up --build
	- docker tag kubernetes-demo-api:latest tanvir909/kubernetes-demo-api:latest
	- docker push tanvir909/kubernetes-demo-api:latest
	- minikube start
	- kubectl get nodes
	- kubectl cluster-info
	- kubectl apply -f k8s/
	- kubectl get pods -w
	- kubectl get services
	- minikube service devops-kubernetes-api-service

tanvir909

