apiVersion: apps/v1
kind: Deployment
metadata:
  name: kubernetes-demo-api
  labels:
    app: kubernetes-demo-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: kubernetes-demo-api
  template:
    metadata:
      labels:
        app: kubernetes-demo-api
    spec:
      containers:
        - name: kubernetes-demo-api
          image: tanvir909/kubernetes-demo-api:latest
          ports:
            - containerPort: 3000
          env:
            - name: NODE_ENV
              value: "production"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          resources:
            requests:
              cpu: "100m"
              memory: "128Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
          readinessProbe:
            httpGet:
              path: /readyz
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /healthz
              port: 3000
            initialDelaySeconds: 10
            periodSeconds: 20