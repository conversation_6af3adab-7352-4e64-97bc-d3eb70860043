
==> Audit <==
┌─────────┬───────────────────────────────┬──────────┬─────────────────────────┬─────────┬─────────────────────┬─────────────────────┐
│ COMMAND │             ARGS              │ PROFILE  │          USER           │ VERSION │     START TIME      │      END TIME       │
├─────────┼───────────────────────────────┼──────────┼─────────────────────────┼─────────┼─────────────────────┼─────────────────────┤
│ start   │                               │ minikube │ AKASHDTH\tanvir.hossain │ v1.37.0 │ 01 Oct 25 11:07 +06 │ 01 Oct 25 11:10 +06 │
│ service │ devops-kubernetes-api-service │ minikube │ AKASHDTH\tanvir.hossain │ v1.37.0 │ 01 Oct 25 11:23 +06 │                     │
│ service │ devops-kubernetes-api-service │ minikube │ AKASHDTH\tanvir.hossain │ v1.37.0 │ 01 Oct 25 11:25 +06 │                     │
│ service │ devops-kubernetes-api-service │ minikube │ AKASHDTH\tanvir.hossain │ v1.37.0 │ 01 Oct 25 11:26 +06 │                     │
└─────────┴───────────────────────────────┴──────────┴─────────────────────────┴─────────┴─────────────────────┴─────────────────────┘


==> Last Start <==
Log file created at: 2025/10/01 11:07:37
Running on machine: bexcom-10272
Binary: Built with gc go1.24.6 for windows/amd64
Log line format: [IWEF]mmdd hh:mm:ss.uuuuuu threadid file:line] msg
I1001 11:07:37.648548   17292 out.go:360] Setting OutFile to fd 116 ...
I1001 11:07:37.649162   17292 out.go:374] Setting ErrFile to fd 120...
W1001 11:07:37.658141   17292 root.go:314] Error reading config file at C:\Users\<USER>\.minikube\config\config.json: open C:\Users\<USER>\.minikube\config\config.json: The system cannot find the file specified.
I1001 11:07:37.663175   17292 out.go:368] Setting JSON to false
I1001 11:07:37.664693   17292 start.go:130] hostinfo: {"hostname":"bexcom-10272","uptime":4164,"bootTime":1759291093,"procs":267,"os":"windows","platform":"Microsoft Windows 11 Enterprise","platformFamily":"Standalone Workstation","platformVersion":"10.0.26100.6725 Build 26100.6725","kernelVersion":"10.0.26100.6725 Build 26100.6725","kernelArch":"x86_64","virtualizationSystem":"","virtualizationRole":"","hostId":"bd996782-e1b3-4aba-aee3-2bc75d7921d9"}
W1001 11:07:37.664693   17292 start.go:138] gopshost.Virtualization returned error: not implemented yet
I1001 11:07:37.666199   17292 out.go:179] 😄  minikube v1.37.0 on Microsoft Windows 11 Enterprise 10.0.26100.6725 Build 26100.6725
W1001 11:07:37.667329   17292 preload.go:293] Failed to list preload files: open C:\Users\<USER>\.minikube\cache\preloaded-tarball: The system cannot find the file specified.
I1001 11:07:37.667329   17292 notify.go:220] Checking for updates...
I1001 11:07:37.667329   17292 driver.go:421] Setting default libvirt URI to qemu:///system
I1001 11:07:37.667329   17292 global.go:112] Querying for installed drivers using PATH=C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\Amazon Corretto\jdk11.0.23_9\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\PuTTY\;C:\xampp\php;C:\ProgramData\ComposerSetup\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin C:\Program Files\OpenSSL-Win64\bin;C:\Program Files\OpenSSL-Win64\bin;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk-17;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\nodejs\;C:\Program Files\Docker\Docker\resources\bin;D:\kubectl;;C:\minikube;D:\minikube;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Program Files\MySQL\MySQL Shell 8.4\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.2.0.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Qoder\bin;D:\kubectl;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
I1001 11:07:40.870622   17292 global.go:133] hyperv default: true priority: 8, state: {Installed:true Healthy:false Running:false NeedsImprovement:false Error:Hyper-V requires Administrator privileges Reason: Fix:Right-click the PowerShell icon and select Run as Administrator to open PowerShell in elevated mode. Doc: Version:}
I1001 11:07:40.879791   17292 global.go:133] qemu2 default: true priority: 3, state: {Installed:false Healthy:false Running:false NeedsImprovement:false Error:exec: "qemu-system-x86_64": executable file not found in %PATH% Reason: Fix:Install qemu-system Doc:https://minikube.sigs.k8s.io/docs/reference/drivers/qemu/ Version:}
I1001 11:07:40.896008   17292 global.go:133] virtualbox default: true priority: 6, state: {Installed:false Healthy:false Running:false NeedsImprovement:false Error:unable to find VBoxManage in $PATH Reason: Fix:Install VirtualBox Doc:https://minikube.sigs.k8s.io/docs/reference/drivers/virtualbox/ Version:}
I1001 11:07:40.904122   17292 global.go:133] vmware default: false priority: 5, state: {Installed:false Healthy:false Running:false NeedsImprovement:false Error:exec: "vmrun": executable file not found in %PATH% Reason: Fix:Install vmrun Doc:https://minikube.sigs.k8s.io/docs/reference/drivers/vmware/ Version:}
I1001 11:07:40.970345   17292 docker.go:123] docker version: linux-28.4.0:Docker Desktop 4.46.0 (204649)
I1001 11:07:40.977512   17292 cli_runner.go:164] Run: docker system info --format "{{json .}}"
I1001 11:07:41.596295   17292 info.go:266] docker info: {ID:************************************ Containers:1 ContainersRunning:0 ContainersPaused:0 ContainersStopped:1 Images:3 Driver:overlayfs DriverStatus:[[driver-type io.containerd.snapshotter.v1]] SystemStatus:<nil> Plugins:{Volume:[local] Network:[bridge host ipvlan macvlan null overlay] Authorization:<nil> Log:[awslogs fluentd gcplogs gelf journald json-file local splunk syslog]} MemoryLimit:true SwapLimit:true KernelMemory:false KernelMemoryTCP:false CPUCfsPeriod:true CPUCfsQuota:true CPUShares:true CPUSet:true PidsLimit:true IPv4Forwarding:true BridgeNfIptables:false BridgeNfIP6Tables:false Debug:false NFd:45 OomKillDisable:false NGoroutines:98 SystemTime:2025-10-01 05:07:41.579469324 +0000 UTC LoggingDriver:json-file CgroupDriver:cgroupfs NEventsListener:12 KernelVersion:********-microsoft-standard-WSL2 OperatingSystem:Docker Desktop OSType:linux Architecture:x86_64 IndexServerAddress:https://index.docker.io/v1/ RegistryConfig:{AllowNondistributableArtifactsCIDRs:[] AllowNondistributableArtifactsHostnames:[] InsecureRegistryCIDRs:[::1/128 *********/8] IndexConfigs:{DockerIo:{Name:docker.io Mirrors:[] Secure:true Official:true}} Mirrors:[]} NCPU:4 MemTotal:********** GenericResources:<nil> DockerRootDir:/var/lib/docker HTTPProxy:http.docker.internal:3128 HTTPSProxy:http.docker.internal:3128 NoProxy:hubproxy.docker.internal Name:docker-desktop Labels:[com.docker.desktop.address=npipe://\\.\pipe\docker_cli] ExperimentalBuild:false ServerVersion:28.4.0 ClusterStore: ClusterAdvertise: Runtimes:{Runc:{Path:runc}} DefaultRuntime:runc Swarm:{NodeID: NodeAddr: LocalNodeState:inactive ControlAvailable:false Error: RemoteManagers:<nil>} LiveRestoreEnabled:false Isolation: InitBinary:docker-init ContainerdCommit:{ID:05044ec0a9a75232cad458027ca83437aae3f4da Expected:} RuncCommit:{ID:v1.2.5-0-g59923ef Expected:} InitCommit:{ID:de40ad0 Expected:} SecurityOptions:[name=seccomp,profile=builtin name=cgroupns] ProductLicense: Warnings:<nil> ServerErrors:[] ClientInfo:{Debug:false Plugins:[map[Name:ai Path:C:\Program Files\Docker\cli-plugins\docker-ai.exe SchemaVersion:0.1.0 ShortDescription:Docker AI Agent - Ask Gordon Vendor:Docker Inc. Version:v1.9.11] map[Name:buildx Path:C:\Program Files\Docker\cli-plugins\docker-buildx.exe SchemaVersion:0.1.0 ShortDescription:Docker Buildx Vendor:Docker Inc. Version:v0.28.0-desktop.1] map[Name:cloud Path:C:\Program Files\Docker\cli-plugins\docker-cloud.exe SchemaVersion:0.1.0 ShortDescription:Docker Cloud Vendor:Docker Inc. Version:v0.4.27] map[Name:compose Path:C:\Program Files\Docker\cli-plugins\docker-compose.exe SchemaVersion:0.1.0 ShortDescription:Docker Compose Vendor:Docker Inc. Version:v2.39.2-desktop.1] map[Name:debug Path:C:\Program Files\Docker\cli-plugins\docker-debug.exe SchemaVersion:0.1.0 ShortDescription:Get a shell into any image or container Vendor:Docker Inc. Version:0.0.42] map[Name:desktop Path:C:\Program Files\Docker\cli-plugins\docker-desktop.exe SchemaVersion:0.1.0 ShortDescription:Docker Desktop commands Vendor:Docker Inc. Version:v0.2.0] map[Name:extension Path:C:\Program Files\Docker\cli-plugins\docker-extension.exe SchemaVersion:0.1.0 ShortDescription:Manages Docker extensions Vendor:Docker Inc. Version:v0.2.31] map[Name:init Path:C:\Program Files\Docker\cli-plugins\docker-init.exe SchemaVersion:0.1.0 ShortDescription:Creates Docker-related starter files for your project Vendor:Docker Inc. Version:v1.4.0] map[Name:mcp Path:C:\Program Files\Docker\cli-plugins\docker-mcp.exe SchemaVersion:0.1.0 ShortDescription:Docker MCP Plugin Vendor:Docker Inc. Version:v0.18.0] map[Name:model Path:C:\Program Files\Docker\cli-plugins\docker-model.exe SchemaVersion:0.1.0 ShortDescription:Docker Model Runner Vendor:Docker Inc. Version:v0.1.40] map[Name:offload Path:C:\Users\<USER>\.docker\cli-plugins\docker-offload.exe SchemaVersion:0.1.0 ShortDescription:Docker Offload Vendor:Docker Inc. Version:v0.4.18] map[Name:sbom Path:C:\Program Files\Docker\cli-plugins\docker-sbom.exe SchemaVersion:0.1.0 ShortDescription:View the packaged-based Software Bill Of Materials (SBOM) for an image URL:https://github.com/docker/sbom-cli-plugin Vendor:Anchore Inc. Version:0.6.0] map[Name:scout Path:C:\Program Files\Docker\cli-plugins\docker-scout.exe SchemaVersion:0.1.0 ShortDescription:Docker Scout Vendor:Docker Inc. Version:v1.18.3]] Warnings:<nil>}}
I1001 11:07:41.596295   17292 global.go:133] docker default: true priority: 9, state: {Installed:true Healthy:true Running:false NeedsImprovement:false Error:<nil> Reason: Fix: Doc: Version:}
I1001 11:07:41.607200   17292 global.go:133] podman default: true priority: 3, state: {Installed:false Healthy:false Running:false NeedsImprovement:false Error:exec: "podman": executable file not found in %PATH% Reason: Fix:Install Podman Doc:https://minikube.sigs.k8s.io/docs/drivers/podman/ Version:}
I1001 11:07:41.607200   17292 global.go:133] ssh default: false priority: 4, state: {Installed:true Healthy:true Running:false NeedsImprovement:false Error:<nil> Reason: Fix: Doc: Version:}
I1001 11:07:41.607200   17292 driver.go:343] not recommending "ssh" due to default: false
I1001 11:07:41.607200   17292 driver.go:338] not recommending "hyperv" due to health: Hyper-V requires Administrator privileges
I1001 11:07:41.607200   17292 driver.go:378] Picked: docker
I1001 11:07:41.607200   17292 driver.go:379] Alternatives: [ssh]
I1001 11:07:41.607200   17292 driver.go:380] Rejects: [hyperv qemu2 virtualbox vmware podman]
I1001 11:07:41.607817   17292 out.go:179] ✨  Automatically selected the docker driver
I1001 11:07:41.608334   17292 start.go:304] selected driver: docker
I1001 11:07:41.608334   17292 start.go:918] validating driver "docker" against <nil>
I1001 11:07:41.608334   17292 start.go:929] status for docker: {Installed:true Healthy:true Running:false NeedsImprovement:false Error:<nil> Reason: Fix: Doc: Version:}
I1001 11:07:41.619729   17292 cli_runner.go:164] Run: docker system info --format "{{json .}}"
I1001 11:07:41.938615   17292 info.go:266] docker info: {ID:************************************ Containers:1 ContainersRunning:0 ContainersPaused:0 ContainersStopped:1 Images:3 Driver:overlayfs DriverStatus:[[driver-type io.containerd.snapshotter.v1]] SystemStatus:<nil> Plugins:{Volume:[local] Network:[bridge host ipvlan macvlan null overlay] Authorization:<nil> Log:[awslogs fluentd gcplogs gelf journald json-file local splunk syslog]} MemoryLimit:true SwapLimit:true KernelMemory:false KernelMemoryTCP:false CPUCfsPeriod:true CPUCfsQuota:true CPUShares:true CPUSet:true PidsLimit:true IPv4Forwarding:true BridgeNfIptables:false BridgeNfIP6Tables:false Debug:false NFd:45 OomKillDisable:false NGoroutines:98 SystemTime:2025-10-01 05:07:41.921859608 +0000 UTC LoggingDriver:json-file CgroupDriver:cgroupfs NEventsListener:12 KernelVersion:********-microsoft-standard-WSL2 OperatingSystem:Docker Desktop OSType:linux Architecture:x86_64 IndexServerAddress:https://index.docker.io/v1/ RegistryConfig:{AllowNondistributableArtifactsCIDRs:[] AllowNondistributableArtifactsHostnames:[] InsecureRegistryCIDRs:[::1/128 *********/8] IndexConfigs:{DockerIo:{Name:docker.io Mirrors:[] Secure:true Official:true}} Mirrors:[]} NCPU:4 MemTotal:********** GenericResources:<nil> DockerRootDir:/var/lib/docker HTTPProxy:http.docker.internal:3128 HTTPSProxy:http.docker.internal:3128 NoProxy:hubproxy.docker.internal Name:docker-desktop Labels:[com.docker.desktop.address=npipe://\\.\pipe\docker_cli] ExperimentalBuild:false ServerVersion:28.4.0 ClusterStore: ClusterAdvertise: Runtimes:{Runc:{Path:runc}} DefaultRuntime:runc Swarm:{NodeID: NodeAddr: LocalNodeState:inactive ControlAvailable:false Error: RemoteManagers:<nil>} LiveRestoreEnabled:false Isolation: InitBinary:docker-init ContainerdCommit:{ID:05044ec0a9a75232cad458027ca83437aae3f4da Expected:} RuncCommit:{ID:v1.2.5-0-g59923ef Expected:} InitCommit:{ID:de40ad0 Expected:} SecurityOptions:[name=seccomp,profile=builtin name=cgroupns] ProductLicense: Warnings:<nil> ServerErrors:[] ClientInfo:{Debug:false Plugins:[map[Name:ai Path:C:\Program Files\Docker\cli-plugins\docker-ai.exe SchemaVersion:0.1.0 ShortDescription:Docker AI Agent - Ask Gordon Vendor:Docker Inc. Version:v1.9.11] map[Name:buildx Path:C:\Program Files\Docker\cli-plugins\docker-buildx.exe SchemaVersion:0.1.0 ShortDescription:Docker Buildx Vendor:Docker Inc. Version:v0.28.0-desktop.1] map[Name:cloud Path:C:\Program Files\Docker\cli-plugins\docker-cloud.exe SchemaVersion:0.1.0 ShortDescription:Docker Cloud Vendor:Docker Inc. Version:v0.4.27] map[Name:compose Path:C:\Program Files\Docker\cli-plugins\docker-compose.exe SchemaVersion:0.1.0 ShortDescription:Docker Compose Vendor:Docker Inc. Version:v2.39.2-desktop.1] map[Name:debug Path:C:\Program Files\Docker\cli-plugins\docker-debug.exe SchemaVersion:0.1.0 ShortDescription:Get a shell into any image or container Vendor:Docker Inc. Version:0.0.42] map[Name:desktop Path:C:\Program Files\Docker\cli-plugins\docker-desktop.exe SchemaVersion:0.1.0 ShortDescription:Docker Desktop commands Vendor:Docker Inc. Version:v0.2.0] map[Name:extension Path:C:\Program Files\Docker\cli-plugins\docker-extension.exe SchemaVersion:0.1.0 ShortDescription:Manages Docker extensions Vendor:Docker Inc. Version:v0.2.31] map[Name:init Path:C:\Program Files\Docker\cli-plugins\docker-init.exe SchemaVersion:0.1.0 ShortDescription:Creates Docker-related starter files for your project Vendor:Docker Inc. Version:v1.4.0] map[Name:mcp Path:C:\Program Files\Docker\cli-plugins\docker-mcp.exe SchemaVersion:0.1.0 ShortDescription:Docker MCP Plugin Vendor:Docker Inc. Version:v0.18.0] map[Name:model Path:C:\Program Files\Docker\cli-plugins\docker-model.exe SchemaVersion:0.1.0 ShortDescription:Docker Model Runner Vendor:Docker Inc. Version:v0.1.40] map[Name:offload Path:C:\Users\<USER>\.docker\cli-plugins\docker-offload.exe SchemaVersion:0.1.0 ShortDescription:Docker Offload Vendor:Docker Inc. Version:v0.4.18] map[Name:sbom Path:C:\Program Files\Docker\cli-plugins\docker-sbom.exe SchemaVersion:0.1.0 ShortDescription:View the packaged-based Software Bill Of Materials (SBOM) for an image URL:https://github.com/docker/sbom-cli-plugin Vendor:Anchore Inc. Version:0.6.0] map[Name:scout Path:C:\Program Files\Docker\cli-plugins\docker-scout.exe SchemaVersion:0.1.0 ShortDescription:Docker Scout Vendor:Docker Inc. Version:v1.18.3]] Warnings:<nil>}}
I1001 11:07:41.939157   17292 start_flags.go:327] no existing cluster config was found, will generate one from the flags 
I1001 11:07:41.979409   17292 start_flags.go:410] Using suggested 4000MB memory alloc based on sys=16051MB, container=7777MB
I1001 11:07:41.979409   17292 start_flags.go:974] Wait components to verify : map[apiserver:true system_pods:true]
I1001 11:07:41.980515   17292 out.go:179] 📌  Using Docker Desktop driver with root privileges
I1001 11:07:41.980515   17292 cni.go:84] Creating CNI manager for ""
I1001 11:07:41.980515   17292 cni.go:158] "docker" driver + "docker" container runtime found on kubernetes v1.24+, recommending bridge
I1001 11:07:41.980515   17292 start_flags.go:336] Found "bridge CNI" CNI - setting NetworkPlugin=cni
I1001 11:07:41.980515   17292 start.go:348] cluster config:
{Name:minikube KeepContext:false EmbedCerts:false MinikubeISO: KicBaseImage:gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 Memory:4000 CPUs:2 DiskSize:20000 Driver:docker HyperkitVpnKitSock: HyperkitVSockPorts:[] DockerEnv:[] ContainerVolumeMounts:[] InsecureRegistry:[] RegistryMirror:[] HostOnlyCIDR:************/24 HypervVirtualSwitch: HypervUseExternalSwitch:false HypervExternalAdapter: KVMNetwork:default KVMQemuURI:qemu:///system KVMGPU:false KVMHidden:false KVMNUMACount:1 APIServerPort:8443 DockerOpt:[] DisableDriverMounts:false NFSShare:[] NFSSharesRoot:/nfsshares UUID: NoVTXCheck:false DNSProxy:false HostDNSResolver:true HostOnlyNicType:virtio NatNicType:virtio SSHIPAddress: SSHUser:root SSHKey: SSHPort:22 KubernetesConfig:{KubernetesVersion:v1.34.0 ClusterName:minikube Namespace:default APIServerHAVIP: APIServerName:minikubeCA APIServerNames:[] APIServerIPs:[] DNSDomain:cluster.local ContainerRuntime:docker CRISocket: NetworkPlugin:cni FeatureGates: ServiceCIDR:*********/12 ImageRepository: LoadBalancerStartIP: LoadBalancerEndIP: CustomIngressCert: RegistryAliases: ExtraOptions:[] ShouldLoadCachedImages:true EnableDefaultCNI:false CNI:} Nodes:[{Name: IP: Port:8443 KubernetesVersion:v1.34.0 ContainerRuntime:docker ControlPlane:true Worker:true}] Addons:map[] CustomAddonImages:map[] CustomAddonRegistries:map[] VerifyComponents:map[apiserver:true system_pods:true] StartHostTimeout:6m0s ScheduledStop:<nil> ExposedPorts:[] ListenAddress: Network: Subnet: MultiNodeRequested:false ExtraDisks:0 CertExpiration:26280h0m0s MountString: Mount9PVersion:9p2000.L MountGID:docker MountIP: MountMSize:262144 MountOptions:[] MountPort:0 MountType:9p MountUID:docker BinaryMirror: DisableOptimizations:false DisableMetrics:false DisableCoreDNSLog:false CustomQemuFirmwarePath: SocketVMnetClientPath: SocketVMnetPath: StaticIP: SSHAuthSock: SSHAgentPID:0 GPUs: AutoPauseInterval:1m0s}
I1001 11:07:41.981089   17292 out.go:179] 👍  Starting "minikube" primary control-plane node in "minikube" cluster
I1001 11:07:41.981605   17292 cache.go:123] Beginning downloading kic base image for docker with docker
I1001 11:07:41.981605   17292 out.go:179] 🚜  Pulling base image v0.0.48 ...
I1001 11:07:41.983158   17292 preload.go:131] Checking if preload exists for k8s version v1.34.0 and runtime docker
I1001 11:07:41.983158   17292 image.go:81] Checking for gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 in local docker daemon
I1001 11:07:42.080532   17292 cache.go:152] Downloading gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 to local cache
I1001 11:07:42.080532   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\kic\amd64\kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1.tar -> C:\Users\<USER>\.minikube\cache\kic\amd64\kicbase_v0.0.48@sha256_7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1.tar
I1001 11:07:42.081050   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\kic\amd64\kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1.tar -> C:\Users\<USER>\.minikube\cache\kic\amd64\kicbase_v0.0.48@sha256_7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1.tar
I1001 11:07:42.081050   17292 image.go:65] Checking for gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 in local cache directory
I1001 11:07:42.081570   17292 image.go:150] Writing gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 to local cache
I1001 11:07:42.309358   17292 preload.go:118] Found remote preload: https://storage.googleapis.com/minikube-preloaded-volume-tarballs/v18/v1.34.0/preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4
I1001 11:07:42.309358   17292 cache.go:58] Caching tarball of preloaded images
I1001 11:07:42.309950   17292 preload.go:131] Checking if preload exists for k8s version v1.34.0 and runtime docker
I1001 11:07:42.311132   17292 out.go:179] 💾  Downloading Kubernetes v1.34.0 preload ...
I1001 11:07:42.312251   17292 preload.go:236] getting checksum for preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4 ...
I1001 11:07:42.800048   17292 download.go:108] Downloading: https://storage.googleapis.com/minikube-preloaded-volume-tarballs/v18/v1.34.0/preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4?checksum=md5:994a4de1464928e89c992dfd0a962e35 -> C:\Users\<USER>\.minikube\cache\preloaded-tarball\preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4
I1001 11:08:06.413877   17292 cache.go:155] successfully saved gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 as a tarball
I1001 11:08:06.413877   17292 cache.go:165] Loading gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 from local cache
I1001 11:08:06.413877   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\kic\amd64\kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1.tar -> C:\Users\<USER>\.minikube\cache\kic\amd64\kicbase_v0.0.48@sha256_7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1.tar
I1001 11:08:30.303518   17292 cache.go:167] successfully loaded and using gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 from cached tarball
W1001 11:09:18.076060   17292 cache.go:64] Error downloading preloaded artifacts will continue without preload: download failed: https://storage.googleapis.com/minikube-preloaded-volume-tarballs/v18/v1.34.0/preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4?checksum=md5:994a4de1464928e89c992dfd0a962e35: getter: &{Ctx:context.Background Src:https://storage.googleapis.com/minikube-preloaded-volume-tarballs/v18/v1.34.0/preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4?checksum=md5:994a4de1464928e89c992dfd0a962e35 Dst:C:\Users\<USER>\.minikube\cache\preloaded-tarball\preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4.download Pwd: Mode:2 Umask:---------- Detectors:[0x609b780 0x609b780 0x609b780 0x609b780 0x609b780 0x609b780 0x609b780] Decompressors:map[bz2:0xc0001905f8 gz:0xc0001906a0 tar:0xc000190630 tar.bz2:0xc000190640 tar.gz:0xc000190650 tar.xz:0xc000190660 tar.zst:0xc000190670 tbz2:0xc000190640 tgz:0xc000190650 txz:0xc000190660 tzst:0xc000190670 xz:0xc0001906a8 zip:0xc0001906b0 zst:0xc0001906c0] Getters:map[file:0xc0016904b0 http:0xc0007ae0a0 https:0xc0007ae0f0] Dir:false ProgressListener:0x603cc60 Insecure:false DisableSymlinks:false Options:[0x17af5c0]}: read tcp *************:60997->**************:443: wsarecv: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
I1001 11:09:18.445569   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-proxy:v1.34.0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-proxy_v1.34.0
I1001 11:09:18.445569   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\coredns\coredns:v1.12.1 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\coredns\coredns_v1.12.1
I1001 11:09:18.445569   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-apiserver:v1.34.0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-apiserver_v1.34.0
I1001 11:09:18.458073   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-controller-manager:v1.34.0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-controller-manager_v1.34.0
I1001 11:09:18.462784   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\gcr.io\k8s-minikube\storage-provisioner:v5 -> C:\Users\<USER>\.minikube\cache\images\amd64\gcr.io\k8s-minikube\storage-provisioner_v5
I1001 11:09:18.445569   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\etcd:3.6.4-0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\etcd_3.6.4-0
I1001 11:09:18.465271   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-scheduler:v1.34.0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-scheduler_v1.34.0
I1001 11:09:18.465271   17292 profile.go:143] Saving config to C:\Users\<USER>\.minikube\profiles\minikube\config.json ...
I1001 11:09:18.468488   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\pause:3.10.1 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\pause_3.10.1
I1001 11:09:18.624687   17292 lock.go:35] WriteFile acquiring C:\Users\<USER>\.minikube\profiles\minikube\config.json: {Name:mkeac59f3116cefb7fe6bccfcffba7c865f655b6 Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:09:19.064428   17292 cache.go:232] Successfully downloaded all kic artifacts
I1001 11:09:19.292924   17292 start.go:360] acquireMachinesLock for minikube: {Name:mk99136f0940ebf1aa6288903179606dfd0cb917 Clock:{} Delay:500ms Timeout:10m0s Cancel:<nil>}
I1001 11:09:19.303611   17292 start.go:364] duration metric: took 10.6874ms to acquireMachinesLock for "minikube"
I1001 11:09:19.384273   17292 start.go:93] Provisioning new machine with config: &{Name:minikube KeepContext:false EmbedCerts:false MinikubeISO: KicBaseImage:gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 Memory:4000 CPUs:2 DiskSize:20000 Driver:docker HyperkitVpnKitSock: HyperkitVSockPorts:[] DockerEnv:[] ContainerVolumeMounts:[] InsecureRegistry:[] RegistryMirror:[] HostOnlyCIDR:************/24 HypervVirtualSwitch: HypervUseExternalSwitch:false HypervExternalAdapter: KVMNetwork:default KVMQemuURI:qemu:///system KVMGPU:false KVMHidden:false KVMNUMACount:1 APIServerPort:8443 DockerOpt:[] DisableDriverMounts:false NFSShare:[] NFSSharesRoot:/nfsshares UUID: NoVTXCheck:false DNSProxy:false HostDNSResolver:true HostOnlyNicType:virtio NatNicType:virtio SSHIPAddress: SSHUser:root SSHKey: SSHPort:22 KubernetesConfig:{KubernetesVersion:v1.34.0 ClusterName:minikube Namespace:default APIServerHAVIP: APIServerName:minikubeCA APIServerNames:[] APIServerIPs:[] DNSDomain:cluster.local ContainerRuntime:docker CRISocket: NetworkPlugin:cni FeatureGates: ServiceCIDR:*********/12 ImageRepository: LoadBalancerStartIP: LoadBalancerEndIP: CustomIngressCert: RegistryAliases: ExtraOptions:[] ShouldLoadCachedImages:true EnableDefaultCNI:false CNI:} Nodes:[{Name: IP: Port:8443 KubernetesVersion:v1.34.0 ContainerRuntime:docker ControlPlane:true Worker:true}] Addons:map[] CustomAddonImages:map[] CustomAddonRegistries:map[] VerifyComponents:map[apiserver:true system_pods:true] StartHostTimeout:6m0s ScheduledStop:<nil> ExposedPorts:[] ListenAddress: Network: Subnet: MultiNodeRequested:false ExtraDisks:0 CertExpiration:26280h0m0s MountString: Mount9PVersion:9p2000.L MountGID:docker MountIP: MountMSize:262144 MountOptions:[] MountPort:0 MountType:9p MountUID:docker BinaryMirror: DisableOptimizations:false DisableMetrics:false DisableCoreDNSLog:false CustomQemuFirmwarePath: SocketVMnetClientPath: SocketVMnetPath: StaticIP: SSHAuthSock: SSHAgentPID:0 GPUs: AutoPauseInterval:1m0s} &{Name: IP: Port:8443 KubernetesVersion:v1.34.0 ContainerRuntime:docker ControlPlane:true Worker:true}
I1001 11:09:19.384273   17292 start.go:125] createHost starting for "" (driver="docker")
I1001 11:09:19.425673   17292 out.go:252] 🔥  Creating docker container (CPUs=2, Memory=4000MB) ...
I1001 11:09:21.032361   17292 start.go:159] libmachine.API.Create for "minikube" (driver="docker")
I1001 11:09:21.032361   17292 client.go:168] LocalClient.Create starting
I1001 11:09:21.967020   17292 main.go:141] libmachine: Creating CA: C:\Users\<USER>\.minikube\certs\ca.pem
I1001 11:09:24.260159   17292 cache.go:107] acquiring lock: {Name:mk65ddffe42051137955249b3038dc642a603cbc Clock:{} Delay:500ms Timeout:10m0s Cancel:<nil>}
I1001 11:09:24.262160   17292 image.go:138] retrieving image: registry.k8s.io/kube-controller-manager:v1.34.0
I1001 11:09:24.266159   17292 cache.go:107] acquiring lock: {Name:mke34c4441c233d6882a68febdc4f8c3564be140 Clock:{} Delay:500ms Timeout:10m0s Cancel:<nil>}
I1001 11:09:24.267940   17292 cache.go:107] acquiring lock: {Name:mkdd66fff885d4779606866bf401dbb7eb28b9e3 Clock:{} Delay:500ms Timeout:10m0s Cancel:<nil>}
I1001 11:09:24.268744   17292 image.go:138] retrieving image: registry.k8s.io/coredns/coredns:v1.12.1
I1001 11:09:24.268744   17292 cache.go:107] acquiring lock: {Name:mka37cf2b1bf42e099517a47182ac2896c25d226 Clock:{} Delay:500ms Timeout:10m0s Cancel:<nil>}
I1001 11:09:24.268744   17292 image.go:138] retrieving image: registry.k8s.io/pause:3.10.1
I1001 11:09:24.269753   17292 image.go:138] retrieving image: registry.k8s.io/kube-scheduler:v1.34.0
I1001 11:09:24.281049   17292 cache.go:107] acquiring lock: {Name:mk262be34326930de5af4ce2d5701dc647bf063a Clock:{} Delay:500ms Timeout:10m0s Cancel:<nil>}
I1001 11:09:24.282130   17292 image.go:138] retrieving image: registry.k8s.io/kube-apiserver:v1.34.0
I1001 11:09:24.355583   17292 image.go:181] daemon lookup for registry.k8s.io/kube-controller-manager:v1.34.0: Error response from daemon: No such image: registry.k8s.io/kube-controller-manager:v1.34.0
I1001 11:09:24.360117   17292 image.go:181] daemon lookup for registry.k8s.io/kube-scheduler:v1.34.0: Error response from daemon: No such image: registry.k8s.io/kube-scheduler:v1.34.0
I1001 11:09:24.364672   17292 image.go:181] daemon lookup for registry.k8s.io/kube-apiserver:v1.34.0: Error response from daemon: No such image: registry.k8s.io/kube-apiserver:v1.34.0
I1001 11:09:24.364672   17292 image.go:181] daemon lookup for registry.k8s.io/pause:3.10.1: Error response from daemon: No such image: registry.k8s.io/pause:3.10.1
I1001 11:09:24.364672   17292 image.go:181] daemon lookup for registry.k8s.io/coredns/coredns:v1.12.1: Error response from daemon: No such image: registry.k8s.io/coredns/coredns:v1.12.1
I1001 11:09:24.378438   17292 cache.go:107] acquiring lock: {Name:mkb5544915874d23c8969fb411408efb41321fa9 Clock:{} Delay:500ms Timeout:10m0s Cancel:<nil>}
I1001 11:09:24.378438   17292 image.go:138] retrieving image: registry.k8s.io/kube-proxy:v1.34.0
I1001 11:09:24.384075   17292 cache.go:107] acquiring lock: {Name:mk28a690da924ac7c64c4f8a6a438c860a809b1d Clock:{} Delay:500ms Timeout:10m0s Cancel:<nil>}
I1001 11:09:24.386544   17292 image.go:138] retrieving image: gcr.io/k8s-minikube/storage-provisioner:v5
I1001 11:09:24.409187   17292 image.go:181] daemon lookup for registry.k8s.io/kube-proxy:v1.34.0: Error response from daemon: No such image: registry.k8s.io/kube-proxy:v1.34.0
I1001 11:09:24.415394   17292 image.go:181] daemon lookup for gcr.io/k8s-minikube/storage-provisioner:v5: Error response from daemon: No such image: gcr.io/k8s-minikube/storage-provisioner:v5
I1001 11:09:24.437520   17292 main.go:141] libmachine: Creating client certificate: C:\Users\<USER>\.minikube\certs\cert.pem
I1001 11:09:24.448129   17292 cache.go:107] acquiring lock: {Name:mk68b9362630cededb65e0a8b1403cc999ef1a5e Clock:{} Delay:500ms Timeout:10m0s Cancel:<nil>}
I1001 11:09:24.448129   17292 image.go:138] retrieving image: registry.k8s.io/etcd:3.6.4-0
I1001 11:09:24.460036   17292 image.go:181] daemon lookup for registry.k8s.io/etcd:3.6.4-0: Error response from daemon: No such image: registry.k8s.io/etcd:3.6.4-0
I1001 11:09:24.660792   17292 cli_runner.go:164] Run: docker network inspect minikube --format "{"Name": "{{.Name}}","Driver": "{{.Driver}}","Subnet": "{{range .IPAM.Config}}{{.Subnet}}{{end}}","Gateway": "{{range .IPAM.Config}}{{.Gateway}}{{end}}","MTU": {{if (index .Options "com.docker.network.driver.mtu")}}{{(index .Options "com.docker.network.driver.mtu")}}{{else}}0{{end}}, "ContainerIPs": [{{range $k,$v := .Containers }}"{{$v.IPv4Address}}",{{end}}]}"
W1001 11:09:24.723937   17292 cli_runner.go:211] docker network inspect minikube --format "{"Name": "{{.Name}}","Driver": "{{.Driver}}","Subnet": "{{range .IPAM.Config}}{{.Subnet}}{{end}}","Gateway": "{{range .IPAM.Config}}{{.Gateway}}{{end}}","MTU": {{if (index .Options "com.docker.network.driver.mtu")}}{{(index .Options "com.docker.network.driver.mtu")}}{{else}}0{{end}}, "ContainerIPs": [{{range $k,$v := .Containers }}"{{$v.IPv4Address}}",{{end}}]}" returned with exit code 1
I1001 11:09:24.732940   17292 network_create.go:284] running [docker network inspect minikube] to gather additional debugging logs...
I1001 11:09:24.732940   17292 cli_runner.go:164] Run: docker network inspect minikube
W1001 11:09:24.792529   17292 cli_runner.go:211] docker network inspect minikube returned with exit code 1
I1001 11:09:24.792529   17292 network_create.go:287] error running [docker network inspect minikube]: docker network inspect minikube: exit status 1
stdout:
[]

stderr:
Error response from daemon: network minikube not found
I1001 11:09:24.792529   17292 network_create.go:289] output of [docker network inspect minikube]: -- stdout --
[]

-- /stdout --
** stderr ** 
Error response from daemon: network minikube not found

** /stderr **
I1001 11:09:24.800219   17292 cli_runner.go:164] Run: docker network inspect bridge --format "{"Name": "{{.Name}}","Driver": "{{.Driver}}","Subnet": "{{range .IPAM.Config}}{{.Subnet}}{{end}}","Gateway": "{{range .IPAM.Config}}{{.Gateway}}{{end}}","MTU": {{if (index .Options "com.docker.network.driver.mtu")}}{{(index .Options "com.docker.network.driver.mtu")}}{{else}}0{{end}}, "ContainerIPs": [{{range $k,$v := .Containers }}"{{$v.IPv4Address}}",{{end}}]}"
I1001 11:09:25.071604   17292 network.go:206] using free private subnet ************/24: &{IP:************ Netmask:************* Prefix:24 CIDR:************/24 Gateway:************ ClientMin:************ ClientMax:************54 Broadcast:************55 IsPrivate:true Interface:{IfaceName: IfaceIPv4: IfaceMTU:0 IfaceMAC:} reservation:0xc0013cea50}
I1001 11:09:25.072127   17292 network_create.go:124] attempt to create docker network minikube ************/24 with gateway ************ and MTU of 1500 ...
I1001 11:09:25.080375   17292 cli_runner.go:164] Run: docker network create --driver=bridge --subnet=************/24 --gateway=************ -o --ip-masq -o --icc -o com.docker.network.driver.mtu=1500 --label=created_by.minikube.sigs.k8s.io=true --label=name.minikube.sigs.k8s.io=minikube minikube
I1001 11:09:25.238791   17292 network_create.go:108] docker network minikube ************/24 created
I1001 11:09:25.241590   17292 kic.go:121] calculated static IP "************" for the "minikube" container
I1001 11:09:25.265591   17292 cli_runner.go:164] Run: docker ps -a --format {{.Names}}
I1001 11:09:25.347687   17292 cli_runner.go:164] Run: docker volume create minikube --label name.minikube.sigs.k8s.io=minikube --label created_by.minikube.sigs.k8s.io=true
I1001 11:09:25.406838   17292 oci.go:103] Successfully created a docker volume minikube
I1001 11:09:25.413735   17292 cli_runner.go:164] Run: docker run --rm --name minikube-preload-sidecar --label created_by.minikube.sigs.k8s.io=true --label name.minikube.sigs.k8s.io=minikube --entrypoint /usr/bin/test -v minikube:/var gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 -d /var/lib
I1001 11:09:25.947467   17292 cache.go:162] opening:  \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\kube-apiserver_v1.34.0
I1001 11:09:26.016379   17292 cache.go:162] opening:  \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\pause_3.10.1
I1001 11:09:26.085974   17292 cache.go:162] opening:  \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\kube-scheduler_v1.34.0
I1001 11:09:26.116956   17292 cache.go:162] opening:  \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\coredns\coredns_v1.12.1
I1001 11:09:26.125779   17292 cache.go:162] opening:  \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\kube-controller-manager_v1.34.0
I1001 11:09:26.151706   17292 cache.go:162] opening:  \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\kube-proxy_v1.34.0
I1001 11:09:26.228659   17292 cache.go:162] opening:  \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\etcd_3.6.4-0
I1001 11:09:26.382320   17292 cache.go:157] \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\pause_3.10.1 exists
I1001 11:09:26.383793   17292 oci.go:107] Successfully prepared a docker volume minikube
I1001 11:09:26.383793   17292 preload.go:131] Checking if preload exists for k8s version v1.34.0 and runtime docker
I1001 11:09:26.386404   17292 kic.go:194] Starting extracting preloaded images to volume ...
I1001 11:09:26.394140   17292 cli_runner.go:164] Run: docker run --rm --entrypoint /usr/bin/tar -v C:\Users\<USER>\.minikube\cache\preloaded-tarball\preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4:/preloaded.tar:ro -v minikube:/extractDir gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 -I lz4 -xf /preloaded.tar -C /extractDir
I1001 11:09:26.399366   17292 cache.go:96] cache image "registry.k8s.io/pause:3.10.1" -> "C:\\Users\\<USER>\\.minikube\\cache\\images\\amd64\\registry.k8s.io\\pause_3.10.1" took 7.9153053s
I1001 11:09:26.401174   17292 cache.go:80] save to tar file registry.k8s.io/pause:3.10.1 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\pause_3.10.1 succeeded
W1001 11:09:27.000269   17292 cli_runner.go:211] docker run --rm --entrypoint /usr/bin/tar -v C:\Users\<USER>\.minikube\cache\preloaded-tarball\preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4:/preloaded.tar:ro -v minikube:/extractDir gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 -I lz4 -xf /preloaded.tar -C /extractDir returned with exit code 2
I1001 11:09:27.000269   17292 kic.go:201] Unable to extract preloaded tarball to volume: docker run --rm --entrypoint /usr/bin/tar -v C:\Users\<USER>\.minikube\cache\preloaded-tarball\preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4:/preloaded.tar:ro -v minikube:/extractDir gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 -I lz4 -xf /preloaded.tar -C /extractDir: exit status 2
stdout:

stderr:
tar (child): /preloaded.tar: Cannot read: Is a directory
tar (child): At beginning of tape, quitting now
tar (child): Error is not recoverable: exiting now
/usr/bin/tar: Child returned status 2
/usr/bin/tar: Error is not recoverable: exiting now
I1001 11:09:27.015312   17292 cli_runner.go:164] Run: docker system info --format "{{json .}}"
I1001 11:09:27.421627   17292 info.go:266] docker info: {ID:************************************ Containers:1 ContainersRunning:0 ContainersPaused:0 ContainersStopped:1 Images:5 Driver:overlayfs DriverStatus:[[driver-type io.containerd.snapshotter.v1]] SystemStatus:<nil> Plugins:{Volume:[local] Network:[bridge host ipvlan macvlan null overlay] Authorization:<nil> Log:[awslogs fluentd gcplogs gelf journald json-file local splunk syslog]} MemoryLimit:true SwapLimit:true KernelMemory:false KernelMemoryTCP:false CPUCfsPeriod:true CPUCfsQuota:true CPUShares:true CPUSet:true PidsLimit:true IPv4Forwarding:true BridgeNfIptables:false BridgeNfIP6Tables:false Debug:false NFd:47 OomKillDisable:false NGoroutines:99 SystemTime:2025-10-01 05:09:27.404074378 +0000 UTC LoggingDriver:json-file CgroupDriver:cgroupfs NEventsListener:12 KernelVersion:********-microsoft-standard-WSL2 OperatingSystem:Docker Desktop OSType:linux Architecture:x86_64 IndexServerAddress:https://index.docker.io/v1/ RegistryConfig:{AllowNondistributableArtifactsCIDRs:[] AllowNondistributableArtifactsHostnames:[] InsecureRegistryCIDRs:[::1/128 *********/8] IndexConfigs:{DockerIo:{Name:docker.io Mirrors:[] Secure:true Official:true}} Mirrors:[]} NCPU:4 MemTotal:********** GenericResources:<nil> DockerRootDir:/var/lib/docker HTTPProxy:http.docker.internal:3128 HTTPSProxy:http.docker.internal:3128 NoProxy:hubproxy.docker.internal Name:docker-desktop Labels:[com.docker.desktop.address=npipe://\\.\pipe\docker_cli] ExperimentalBuild:false ServerVersion:28.4.0 ClusterStore: ClusterAdvertise: Runtimes:{Runc:{Path:runc}} DefaultRuntime:runc Swarm:{NodeID: NodeAddr: LocalNodeState:inactive ControlAvailable:false Error: RemoteManagers:<nil>} LiveRestoreEnabled:false Isolation: InitBinary:docker-init ContainerdCommit:{ID:05044ec0a9a75232cad458027ca83437aae3f4da Expected:} RuncCommit:{ID:v1.2.5-0-g59923ef Expected:} InitCommit:{ID:de40ad0 Expected:} SecurityOptions:[name=seccomp,profile=builtin name=cgroupns] ProductLicense: Warnings:<nil> ServerErrors:[] ClientInfo:{Debug:false Plugins:[map[Name:ai Path:C:\Program Files\Docker\cli-plugins\docker-ai.exe SchemaVersion:0.1.0 ShortDescription:Docker AI Agent - Ask Gordon Vendor:Docker Inc. Version:v1.9.11] map[Name:buildx Path:C:\Program Files\Docker\cli-plugins\docker-buildx.exe SchemaVersion:0.1.0 ShortDescription:Docker Buildx Vendor:Docker Inc. Version:v0.28.0-desktop.1] map[Name:cloud Path:C:\Program Files\Docker\cli-plugins\docker-cloud.exe SchemaVersion:0.1.0 ShortDescription:Docker Cloud Vendor:Docker Inc. Version:v0.4.27] map[Name:compose Path:C:\Program Files\Docker\cli-plugins\docker-compose.exe SchemaVersion:0.1.0 ShortDescription:Docker Compose Vendor:Docker Inc. Version:v2.39.2-desktop.1] map[Name:debug Path:C:\Program Files\Docker\cli-plugins\docker-debug.exe SchemaVersion:0.1.0 ShortDescription:Get a shell into any image or container Vendor:Docker Inc. Version:0.0.42] map[Name:desktop Path:C:\Program Files\Docker\cli-plugins\docker-desktop.exe SchemaVersion:0.1.0 ShortDescription:Docker Desktop commands Vendor:Docker Inc. Version:v0.2.0] map[Name:extension Path:C:\Program Files\Docker\cli-plugins\docker-extension.exe SchemaVersion:0.1.0 ShortDescription:Manages Docker extensions Vendor:Docker Inc. Version:v0.2.31] map[Name:init Path:C:\Program Files\Docker\cli-plugins\docker-init.exe SchemaVersion:0.1.0 ShortDescription:Creates Docker-related starter files for your project Vendor:Docker Inc. Version:v1.4.0] map[Name:mcp Path:C:\Program Files\Docker\cli-plugins\docker-mcp.exe SchemaVersion:0.1.0 ShortDescription:Docker MCP Plugin Vendor:Docker Inc. Version:v0.18.0] map[Name:model Path:C:\Program Files\Docker\cli-plugins\docker-model.exe SchemaVersion:0.1.0 ShortDescription:Docker Model Runner Vendor:Docker Inc. Version:v0.1.40] map[Name:offload Path:C:\Users\<USER>\.docker\cli-plugins\docker-offload.exe SchemaVersion:0.1.0 ShortDescription:Docker Offload Vendor:Docker Inc. Version:v0.4.18] map[Name:sbom Path:C:\Program Files\Docker\cli-plugins\docker-sbom.exe SchemaVersion:0.1.0 ShortDescription:View the packaged-based Software Bill Of Materials (SBOM) for an image URL:https://github.com/docker/sbom-cli-plugin Vendor:Anchore Inc. Version:0.6.0] map[Name:scout Path:C:\Program Files\Docker\cli-plugins\docker-scout.exe SchemaVersion:0.1.0 ShortDescription:Docker Scout Vendor:Docker Inc. Version:v1.18.3]] Warnings:<nil>}}
I1001 11:09:27.430102   17292 cli_runner.go:164] Run: docker info --format "'{{json .SecurityOptions}}'"
I1001 11:09:27.564438   17292 cache.go:162] opening:  \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\gcr.io\k8s-minikube\storage-provisioner_v5
I1001 11:09:27.836593   17292 cli_runner.go:164] Run: docker run -d -t --privileged --security-opt seccomp=unconfined --tmpfs /tmp --tmpfs /run -v /lib/modules:/lib/modules:ro --hostname minikube --name minikube --label created_by.minikube.sigs.k8s.io=true --label name.minikube.sigs.k8s.io=minikube --label role.minikube.sigs.k8s.io= --label mode.minikube.sigs.k8s.io=minikube --network minikube --ip ************ --volume minikube:/var --security-opt apparmor=unconfined --memory=4000mb --memory-swap=4000mb --cpus=2 -e container=docker --expose 8443 --publish=127.0.0.1::8443 --publish=127.0.0.1::22 --publish=127.0.0.1::2376 --publish=127.0.0.1::5000 --publish=127.0.0.1::32443 gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1
I1001 11:09:28.441455   17292 cli_runner.go:164] Run: docker container inspect minikube --format={{.State.Running}}
I1001 11:09:28.524680   17292 cli_runner.go:164] Run: docker container inspect minikube --format={{.State.Status}}
I1001 11:09:28.615043   17292 cli_runner.go:164] Run: docker exec minikube stat /var/lib/dpkg/alternatives/iptables
I1001 11:09:28.800005   17292 oci.go:144] the created container "minikube" has a running status.
I1001 11:09:28.800553   17292 kic.go:225] Creating ssh key for kic: C:\Users\<USER>\.minikube\machines\minikube\id_rsa...
I1001 11:09:29.059028   17292 kic_runner.go:191] docker (temp): C:\Users\<USER>\.minikube\machines\minikube\id_rsa.pub --> /home/<USER>/.ssh/authorized_keys (381 bytes)
I1001 11:09:29.219431   17292 cli_runner.go:164] Run: docker container inspect minikube --format={{.State.Status}}
I1001 11:09:29.427124   17292 kic_runner.go:93] Run: chown docker:docker /home/<USER>/.ssh/authorized_keys
I1001 11:09:29.427124   17292 kic_runner.go:114] Args: [docker exec --privileged minikube chown docker:docker /home/<USER>/.ssh/authorized_keys]
I1001 11:09:29.605465   17292 cache.go:157] \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\gcr.io\k8s-minikube\storage-provisioner_v5 exists
I1001 11:09:29.605465   17292 cache.go:96] cache image "gcr.io/k8s-minikube/storage-provisioner:v5" -> "C:\\Users\\<USER>\\.minikube\\cache\\images\\amd64\\gcr.io\\k8s-minikube\\storage-provisioner_v5" took 11.1426804s
I1001 11:09:29.605465   17292 cache.go:80] save to tar file gcr.io/k8s-minikube/storage-provisioner:v5 -> C:\Users\<USER>\.minikube\cache\images\amd64\gcr.io\k8s-minikube\storage-provisioner_v5 succeeded
I1001 11:09:29.637026   17292 kic.go:265] ensuring only current user has permissions to key file located at : C:\Users\<USER>\.minikube\machines\minikube\id_rsa...
I1001 11:09:30.602305   17292 cache.go:157] \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\kube-proxy_v1.34.0 exists
I1001 11:09:30.612970   17292 cache.go:96] cache image "registry.k8s.io/kube-proxy:v1.34.0" -> "C:\\Users\\<USER>\\.minikube\\cache\\images\\amd64\\registry.k8s.io\\kube-proxy_v1.34.0" took 12.1674011s
I1001 11:09:30.613737   17292 cache.go:80] save to tar file registry.k8s.io/kube-proxy:v1.34.0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-proxy_v1.34.0 succeeded
I1001 11:09:32.384271   17292 cache.go:157] \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\kube-scheduler_v1.34.0 exists
I1001 11:09:32.384776   17292 cache.go:96] cache image "registry.k8s.io/kube-scheduler:v1.34.0" -> "C:\\Users\\<USER>\\.minikube\\cache\\images\\amd64\\registry.k8s.io\\kube-scheduler_v1.34.0" took 13.9195054s
I1001 11:09:32.384776   17292 cache.go:80] save to tar file registry.k8s.io/kube-scheduler:v1.34.0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-scheduler_v1.34.0 succeeded
I1001 11:09:32.467439   17292 cache.go:157] \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\coredns\coredns_v1.12.1 exists
I1001 11:09:32.468176   17292 cache.go:96] cache image "registry.k8s.io/coredns/coredns:v1.12.1" -> "C:\\Users\\<USER>\\.minikube\\cache\\images\\amd64\\registry.k8s.io\\coredns\\coredns_v1.12.1" took 14.0218695s
I1001 11:09:32.468176   17292 cache.go:80] save to tar file registry.k8s.io/coredns/coredns:v1.12.1 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\coredns\coredns_v1.12.1 succeeded
I1001 11:09:32.606485   17292 cli_runner.go:164] Run: docker container inspect minikube --format={{.State.Status}}
I1001 11:09:32.654711   17292 machine.go:93] provisionDockerMachine start ...
I1001 11:09:32.674155   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "22/tcp") 0).HostPort}}'" minikube
I1001 11:09:32.687879   17292 cache.go:157] \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\kube-controller-manager_v1.34.0 exists
I1001 11:09:32.687879   17292 cache.go:96] cache image "registry.k8s.io/kube-controller-manager:v1.34.0" -> "C:\\Users\\<USER>\\.minikube\\cache\\images\\amd64\\registry.k8s.io\\kube-controller-manager_v1.34.0" took 14.2298065s
I1001 11:09:32.687879   17292 cache.go:80] save to tar file registry.k8s.io/kube-controller-manager:v1.34.0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-controller-manager_v1.34.0 succeeded
I1001 11:09:32.732857   17292 main.go:141] libmachine: Using SSH client type: native
I1001 11:09:32.747255   17292 main.go:141] libmachine: &{{{<nil> 0 [] [] []} docker [0x5a17c0] 0x5a4300 <nil>  [] 0s} 127.0.0.1 54350 <nil> <nil>}
I1001 11:09:32.747255   17292 main.go:141] libmachine: About to run SSH command:
hostname
I1001 11:09:32.929189   17292 main.go:141] libmachine: SSH cmd err, output: <nil>: minikube

I1001 11:09:32.931311   17292 ubuntu.go:182] provisioning hostname "minikube"
I1001 11:09:32.942005   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "22/tcp") 0).HostPort}}'" minikube
I1001 11:09:32.999157   17292 main.go:141] libmachine: Using SSH client type: native
I1001 11:09:32.999676   17292 main.go:141] libmachine: &{{{<nil> 0 [] [] []} docker [0x5a17c0] 0x5a4300 <nil>  [] 0s} 127.0.0.1 54350 <nil> <nil>}
I1001 11:09:32.999676   17292 main.go:141] libmachine: About to run SSH command:
sudo hostname minikube && echo "minikube" | sudo tee /etc/hostname
I1001 11:09:33.230840   17292 main.go:141] libmachine: SSH cmd err, output: <nil>: minikube

I1001 11:09:33.330730   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "22/tcp") 0).HostPort}}'" minikube
I1001 11:09:33.390900   17292 main.go:141] libmachine: Using SSH client type: native
I1001 11:09:33.391475   17292 main.go:141] libmachine: &{{{<nil> 0 [] [] []} docker [0x5a17c0] 0x5a4300 <nil>  [] 0s} 127.0.0.1 54350 <nil> <nil>}
I1001 11:09:33.391475   17292 main.go:141] libmachine: About to run SSH command:

		if ! grep -xq '.*\sminikube' /etc/hosts; then
			if grep -xq '*********\s.*' /etc/hosts; then
				sudo sed -i 's/^*********\s.*/********* minikube/g' /etc/hosts;
			else 
				echo '********* minikube' | sudo tee -a /etc/hosts; 
			fi
		fi
I1001 11:09:33.557261   17292 main.go:141] libmachine: SSH cmd err, output: <nil>: 
I1001 11:09:33.557261   17292 ubuntu.go:188] set auth options {CertDir:C:\Users\<USER>\.minikube CaCertPath:C:\Users\<USER>\.minikube\certs\ca.pem CaPrivateKeyPath:C:\Users\<USER>\.minikube\certs\ca-key.pem CaCertRemotePath:/etc/docker/ca.pem ServerCertPath:C:\Users\<USER>\.minikube\machines\server.pem ServerKeyPath:C:\Users\<USER>\.minikube\machines\server-key.pem ClientKeyPath:C:\Users\<USER>\.minikube\certs\key.pem ServerCertRemotePath:/etc/docker/server.pem ServerKeyRemotePath:/etc/docker/server-key.pem ClientCertPath:C:\Users\<USER>\.minikube\certs\cert.pem ServerCertSANs:[] StorePath:C:\Users\<USER>\.minikube}
I1001 11:09:33.557261   17292 ubuntu.go:190] setting up certificates
I1001 11:09:33.557261   17292 provision.go:84] configureAuth start
I1001 11:09:33.564698   17292 cli_runner.go:164] Run: docker container inspect -f "{{range .NetworkSettings.Networks}}{{.IPAddress}},{{.GlobalIPv6Address}}{{end}}" minikube
I1001 11:09:33.642455   17292 provision.go:143] copyHostCerts
I1001 11:09:33.643543   17292 exec_runner.go:151] cp: C:\Users\<USER>\.minikube\certs\ca.pem --> C:\Users\<USER>\.minikube/ca.pem (1099 bytes)
I1001 11:09:33.646577   17292 exec_runner.go:151] cp: C:\Users\<USER>\.minikube\certs\cert.pem --> C:\Users\<USER>\.minikube/cert.pem (1143 bytes)
I1001 11:09:33.648093   17292 exec_runner.go:151] cp: C:\Users\<USER>\.minikube\certs\key.pem --> C:\Users\<USER>\.minikube/key.pem (1675 bytes)
I1001 11:09:33.649603   17292 provision.go:117] generating server cert: C:\Users\<USER>\.minikube\machines\server.pem ca-key=C:\Users\<USER>\.minikube\certs\ca.pem private-key=C:\Users\<USER>\.minikube\certs\ca-key.pem org=tanvir.hossain.minikube san=[127.0.0.1 ************ localhost minikube]
I1001 11:09:33.905241   17292 provision.go:177] copyRemoteCerts
I1001 11:09:33.907254   17292 ssh_runner.go:195] Run: sudo mkdir -p /etc/docker /etc/docker /etc/docker
I1001 11:09:33.914995   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "22/tcp") 0).HostPort}}'" minikube
I1001 11:09:33.978792   17292 sshutil.go:53] new ssh client: &{IP:127.0.0.1 Port:54350 SSHKeyPath:C:\Users\<USER>\.minikube\machines\minikube\id_rsa Username:docker}
I1001 11:09:34.088149   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\machines\server.pem --> /etc/docker/server.pem (1200 bytes)
I1001 11:09:34.127456   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\machines\server-key.pem --> /etc/docker/server-key.pem (1679 bytes)
I1001 11:09:34.173400   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\certs\ca.pem --> /etc/docker/ca.pem (1099 bytes)
I1001 11:09:34.225583   17292 provision.go:87] duration metric: took 666.1896ms to configureAuth
I1001 11:09:34.225583   17292 ubuntu.go:206] setting minikube options for container-runtime
I1001 11:09:34.226400   17292 config.go:182] Loaded profile config "minikube": Driver=docker, ContainerRuntime=docker, KubernetesVersion=v1.34.0
I1001 11:09:34.236463   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "22/tcp") 0).HostPort}}'" minikube
I1001 11:09:34.318203   17292 main.go:141] libmachine: Using SSH client type: native
I1001 11:09:34.318203   17292 main.go:141] libmachine: &{{{<nil> 0 [] [] []} docker [0x5a17c0] 0x5a4300 <nil>  [] 0s} 127.0.0.1 54350 <nil> <nil>}
I1001 11:09:34.318203   17292 main.go:141] libmachine: About to run SSH command:
df --output=fstype / | tail -n 1
I1001 11:09:34.478073   17292 main.go:141] libmachine: SSH cmd err, output: <nil>: overlay

I1001 11:09:34.478073   17292 ubuntu.go:71] root file system type: overlay
I1001 11:09:34.479269   17292 provision.go:314] Updating docker unit: /lib/systemd/system/docker.service ...
I1001 11:09:34.486235   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "22/tcp") 0).HostPort}}'" minikube
I1001 11:09:34.539929   17292 main.go:141] libmachine: Using SSH client type: native
I1001 11:09:34.540462   17292 main.go:141] libmachine: &{{{<nil> 0 [] [] []} docker [0x5a17c0] 0x5a4300 <nil>  [] 0s} 127.0.0.1 54350 <nil> <nil>}
I1001 11:09:34.540462   17292 main.go:141] libmachine: About to run SSH command:
sudo mkdir -p /lib/systemd/system && printf %s "[Unit]
Description=Docker Application Container Engine
Documentation=https://docs.docker.com
After=network-online.target nss-lookup.target docker.socket firewalld.service containerd.service time-set.target
Wants=network-online.target containerd.service
Requires=docker.socket
StartLimitBurst=3
StartLimitIntervalSec=60

[Service]
Type=notify
Restart=always



# This file is a systemd drop-in unit that inherits from the base dockerd configuration.
# The base configuration already specifies an 'ExecStart=...' command. The first directive
# here is to clear out that command inherited from the base configuration. Without this,
# the command from the base configuration and the command specified here are treated as
# a sequence of commands, which is not the desired behavior, nor is it valid -- systemd
# will catch this invalid input and refuse to start the service with an error like:
#  Service has more than one ExecStart= setting, which is only allowed for Type=oneshot services.

# NOTE: default-ulimit=nofile is set to an arbitrary number for consistency with other
# container runtimes. If left unlimited, it may result in OOM issues with MySQL.
ExecStart=
ExecStart=/usr/bin/dockerd -H tcp://0.0.0.0:2376 \
	-H fd:// --containerd=/run/containerd/containerd.sock \
	-H unix:///var/run/docker.sock \
	--default-ulimit=nofile=1048576:1048576 \
	--tlsverify \
	--tlscacert /etc/docker/ca.pem \
	--tlscert /etc/docker/server.pem \
	--tlskey /etc/docker/server-key.pem --label provider=docker --insecure-registry *********/12 
ExecReload=/bin/kill -s HUP \$MAINPID

# Having non-zero Limit*s causes performance problems due to accounting overhead
# in the kernel. We recommend using cgroups to do container-local accounting.
LimitNOFILE=infinity
LimitNPROC=infinity
LimitCORE=infinity

# Uncomment TasksMax if your systemd version supports it.
# Only systemd 226 and above support this version.
TasksMax=infinity
TimeoutStartSec=0

# set delegate yes so that systemd does not reset the cgroups of docker containers
Delegate=yes

# kill only the docker process, not all processes in the cgroup
KillMode=process
OOMScoreAdjust=-500

[Install]
WantedBy=multi-user.target
" | sudo tee /lib/systemd/system/docker.service.new
I1001 11:09:34.716169   17292 main.go:141] libmachine: SSH cmd err, output: <nil>: [Unit]
Description=Docker Application Container Engine
Documentation=https://docs.docker.com
After=network-online.target nss-lookup.target docker.socket firewalld.service containerd.service time-set.target
Wants=network-online.target containerd.service
Requires=docker.socket
StartLimitBurst=3
StartLimitIntervalSec=60

[Service]
Type=notify
Restart=always



# This file is a systemd drop-in unit that inherits from the base dockerd configuration.
# The base configuration already specifies an 'ExecStart=...' command. The first directive
# here is to clear out that command inherited from the base configuration. Without this,
# the command from the base configuration and the command specified here are treated as
# a sequence of commands, which is not the desired behavior, nor is it valid -- systemd
# will catch this invalid input and refuse to start the service with an error like:
#  Service has more than one ExecStart= setting, which is only allowed for Type=oneshot services.

# NOTE: default-ulimit=nofile is set to an arbitrary number for consistency with other
# container runtimes. If left unlimited, it may result in OOM issues with MySQL.
ExecStart=
ExecStart=/usr/bin/dockerd -H tcp://0.0.0.0:2376 	-H fd:// --containerd=/run/containerd/containerd.sock 	-H unix:///var/run/docker.sock 	--default-ulimit=nofile=1048576:1048576 	--tlsverify 	--tlscacert /etc/docker/ca.pem 	--tlscert /etc/docker/server.pem 	--tlskey /etc/docker/server-key.pem --label provider=docker --insecure-registry *********/12 
ExecReload=/bin/kill -s HUP $MAINPID

# Having non-zero Limit*s causes performance problems due to accounting overhead
# in the kernel. We recommend using cgroups to do container-local accounting.
LimitNOFILE=infinity
LimitNPROC=infinity
LimitCORE=infinity

# Uncomment TasksMax if your systemd version supports it.
# Only systemd 226 and above support this version.
TasksMax=infinity
TimeoutStartSec=0

# set delegate yes so that systemd does not reset the cgroups of docker containers
Delegate=yes

# kill only the docker process, not all processes in the cgroup
KillMode=process
OOMScoreAdjust=-500

[Install]
WantedBy=multi-user.target

I1001 11:09:34.745400   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "22/tcp") 0).HostPort}}'" minikube
I1001 11:09:34.831853   17292 main.go:141] libmachine: Using SSH client type: native
I1001 11:09:34.832387   17292 main.go:141] libmachine: &{{{<nil> 0 [] [] []} docker [0x5a17c0] 0x5a4300 <nil>  [] 0s} 127.0.0.1 54350 <nil> <nil>}
I1001 11:09:34.832387   17292 main.go:141] libmachine: About to run SSH command:
sudo diff -u /lib/systemd/system/docker.service /lib/systemd/system/docker.service.new || { sudo mv /lib/systemd/system/docker.service.new /lib/systemd/system/docker.service; sudo systemctl -f daemon-reload && sudo systemctl -f enable docker && sudo systemctl -f restart docker; }
I1001 11:09:35.553291   17292 cache.go:157] \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\etcd_3.6.4-0 exists
I1001 11:09:35.553799   17292 cache.go:96] cache image "registry.k8s.io/etcd:3.6.4-0" -> "C:\\Users\\<USER>\\.minikube\\cache\\images\\amd64\\registry.k8s.io\\etcd_3.6.4-0" took 17.0910146s
I1001 11:09:35.553799   17292 cache.go:80] save to tar file registry.k8s.io/etcd:3.6.4-0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\etcd_3.6.4-0 succeeded
I1001 11:09:37.028191   17292 cache.go:157] \\?\Volume{e5a044cd-ed51-41b6-8edb-15686f40ff0b}\Users\tanvir.hossain\.minikube\cache\images\amd64\registry.k8s.io\kube-apiserver_v1.34.0 exists
I1001 11:09:37.041232   17292 cache.go:96] cache image "registry.k8s.io/kube-apiserver:v1.34.0" -> "C:\\Users\\<USER>\\.minikube\\cache\\images\\amd64\\registry.k8s.io\\kube-apiserver_v1.34.0" took 18.5956629s
I1001 11:09:37.041232   17292 cache.go:80] save to tar file registry.k8s.io/kube-apiserver:v1.34.0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-apiserver_v1.34.0 succeeded
I1001 11:09:37.041232   17292 cache.go:87] Successfully saved all images to host disk.
I1001 11:09:37.473416   17292 main.go:141] libmachine: SSH cmd err, output: <nil>: --- /lib/systemd/system/docker.service	2025-09-03 20:55:49.000000000 +0000
+++ /lib/systemd/system/docker.service.new	2025-10-01 05:09:34.709593755 +0000
@@ -9,23 +9,34 @@
 
 [Service]
 Type=notify
-# the default is not to use systemd for cgroups because the delegate issues still
-# exists and systemd currently does not support the cgroup feature set required
-# for containers run by docker
-ExecStart=/usr/bin/dockerd -H fd:// --containerd=/run/containerd/containerd.sock
-ExecReload=/bin/kill -s HUP $MAINPID
-TimeoutStartSec=0
-RestartSec=2
 Restart=always
 
+
+
+# This file is a systemd drop-in unit that inherits from the base dockerd configuration.
+# The base configuration already specifies an 'ExecStart=...' command. The first directive
+# here is to clear out that command inherited from the base configuration. Without this,
+# the command from the base configuration and the command specified here are treated as
+# a sequence of commands, which is not the desired behavior, nor is it valid -- systemd
+# will catch this invalid input and refuse to start the service with an error like:
+#  Service has more than one ExecStart= setting, which is only allowed for Type=oneshot services.
+
+# NOTE: default-ulimit=nofile is set to an arbitrary number for consistency with other
+# container runtimes. If left unlimited, it may result in OOM issues with MySQL.
+ExecStart=
+ExecStart=/usr/bin/dockerd -H tcp://0.0.0.0:2376 	-H fd:// --containerd=/run/containerd/containerd.sock 	-H unix:///var/run/docker.sock 	--default-ulimit=nofile=1048576:1048576 	--tlsverify 	--tlscacert /etc/docker/ca.pem 	--tlscert /etc/docker/server.pem 	--tlskey /etc/docker/server-key.pem --label provider=docker --insecure-registry *********/12 
+ExecReload=/bin/kill -s HUP $MAINPID
+
 # Having non-zero Limit*s causes performance problems due to accounting overhead
 # in the kernel. We recommend using cgroups to do container-local accounting.
+LimitNOFILE=infinity
 LimitNPROC=infinity
 LimitCORE=infinity
 
-# Comment TasksMax if your systemd version does not support it.
-# Only systemd 226 and above support this option.
+# Uncomment TasksMax if your systemd version supports it.
+# Only systemd 226 and above support this version.
 TasksMax=infinity
+TimeoutStartSec=0
 
 # set delegate yes so that systemd does not reset the cgroups of docker containers
 Delegate=yes
Synchronizing state of docker.service with SysV service script with /lib/systemd/systemd-sysv-install.
Executing: /lib/systemd/systemd-sysv-install enable docker

I1001 11:09:37.473416   17292 machine.go:96] duration metric: took 4.8187046s to provisionDockerMachine
I1001 11:09:37.473416   17292 client.go:171] duration metric: took 16.4410546s to LocalClient.Create
I1001 11:09:37.473947   17292 start.go:167] duration metric: took 16.4410546s to libmachine.API.Create "minikube"
I1001 11:09:37.473947   17292 start.go:293] postStartSetup for "minikube" (driver="docker")
I1001 11:09:37.473947   17292 start.go:322] creating required directories: [/etc/kubernetes/addons /etc/kubernetes/manifests /var/tmp/minikube /var/lib/minikube /var/lib/minikube/certs /var/lib/minikube/images /var/lib/minikube/binaries /tmp/gvisor /usr/share/ca-certificates /etc/ssl/certs]
I1001 11:09:37.476189   17292 ssh_runner.go:195] Run: sudo mkdir -p /etc/kubernetes/addons /etc/kubernetes/manifests /var/tmp/minikube /var/lib/minikube /var/lib/minikube/certs /var/lib/minikube/images /var/lib/minikube/binaries /tmp/gvisor /usr/share/ca-certificates /etc/ssl/certs
I1001 11:09:37.491001   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "22/tcp") 0).HostPort}}'" minikube
I1001 11:09:37.607768   17292 sshutil.go:53] new ssh client: &{IP:127.0.0.1 Port:54350 SSHKeyPath:C:\Users\<USER>\.minikube\machines\minikube\id_rsa Username:docker}
I1001 11:09:37.792876   17292 ssh_runner.go:195] Run: cat /etc/os-release
I1001 11:09:37.800020   17292 main.go:141] libmachine: Couldn't set key VERSION_CODENAME, no corresponding struct field found
I1001 11:09:37.800020   17292 main.go:141] libmachine: Couldn't set key PRIVACY_POLICY_URL, no corresponding struct field found
I1001 11:09:37.800020   17292 main.go:141] libmachine: Couldn't set key UBUNTU_CODENAME, no corresponding struct field found
I1001 11:09:37.800020   17292 info.go:137] Remote host: Ubuntu 22.04.5 LTS
I1001 11:09:37.800553   17292 filesync.go:126] Scanning C:\Users\<USER>\.minikube\addons for local assets ...
I1001 11:09:37.801085   17292 filesync.go:126] Scanning C:\Users\<USER>\.minikube\files for local assets ...
I1001 11:09:37.801085   17292 start.go:296] duration metric: took 327.138ms for postStartSetup
I1001 11:09:37.822868   17292 cli_runner.go:164] Run: docker container inspect -f "{{range .NetworkSettings.Networks}}{{.IPAddress}},{{.GlobalIPv6Address}}{{end}}" minikube
I1001 11:09:37.909854   17292 profile.go:143] Saving config to C:\Users\<USER>\.minikube\profiles\minikube\config.json ...
I1001 11:09:37.933928   17292 ssh_runner.go:195] Run: sh -c "df -h /var | awk 'NR==2{print $5}'"
I1001 11:09:37.948569   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "22/tcp") 0).HostPort}}'" minikube
I1001 11:09:38.174173   17292 sshutil.go:53] new ssh client: &{IP:127.0.0.1 Port:54350 SSHKeyPath:C:\Users\<USER>\.minikube\machines\minikube\id_rsa Username:docker}
I1001 11:09:38.354302   17292 ssh_runner.go:195] Run: sh -c "df -BG /var | awk 'NR==2{print $4}'"
I1001 11:09:38.366138   17292 start.go:128] duration metric: took 18.981865s to createHost
I1001 11:09:38.366138   17292 start.go:83] releasing machines lock for "minikube", held for 19.0625269s
I1001 11:09:38.383743   17292 cli_runner.go:164] Run: docker container inspect -f "{{range .NetworkSettings.Networks}}{{.IPAddress}},{{.GlobalIPv6Address}}{{end}}" minikube
I1001 11:09:38.464205   17292 ssh_runner.go:195] Run: curl.exe -sS -m 2 https://registry.k8s.io/
I1001 11:09:38.480808   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "22/tcp") 0).HostPort}}'" minikube
I1001 11:09:38.481341   17292 ssh_runner.go:195] Run: cat /version.json
I1001 11:09:38.494771   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "22/tcp") 0).HostPort}}'" minikube
I1001 11:09:38.575274   17292 sshutil.go:53] new ssh client: &{IP:127.0.0.1 Port:54350 SSHKeyPath:C:\Users\<USER>\.minikube\machines\minikube\id_rsa Username:docker}
I1001 11:09:38.585031   17292 sshutil.go:53] new ssh client: &{IP:127.0.0.1 Port:54350 SSHKeyPath:C:\Users\<USER>\.minikube\machines\minikube\id_rsa Username:docker}
W1001 11:09:38.711244   17292 start.go:868] [curl.exe -sS -m 2 https://registry.k8s.io/] failed: curl.exe -sS -m 2 https://registry.k8s.io/: Process exited with status 127
stdout:

stderr:
bash: line 1: curl.exe: command not found
I1001 11:09:38.736693   17292 ssh_runner.go:195] Run: systemctl --version
I1001 11:09:38.846109   17292 ssh_runner.go:195] Run: sh -c "stat /etc/cni/net.d/*loopback.conf*"
I1001 11:09:38.857665   17292 ssh_runner.go:195] Run: sudo find \etc\cni\net.d -maxdepth 1 -type f -name *loopback.conf* -not -name *.mk_disabled -exec sh -c "grep -q loopback {} && ( grep -q name {} || sudo sed -i '/"type": "loopback"/i \ \ \ \ "name": "loopback",' {} ) && sudo sed -i 's|"cniVersion": ".*"|"cniVersion": "1.0.0"|g' {}" ;
W1001 11:09:38.873720   17292 start.go:439] unable to name loopback interface in configureRuntimes: unable to patch loopback cni config "/etc/cni/net.d/*loopback.conf*": sudo find \etc\cni\net.d -maxdepth 1 -type f -name *loopback.conf* -not -name *.mk_disabled -exec sh -c "grep -q loopback {} && ( grep -q name {} || sudo sed -i '/"type": "loopback"/i \ \ \ \ "name": "loopback",' {} ) && sudo sed -i 's|"cniVersion": ".*"|"cniVersion": "1.0.0"|g' {}" ;: Process exited with status 1
stdout:

stderr:
find: '\\etc\\cni\\net.d': No such file or directory
I1001 11:09:38.876479   17292 ssh_runner.go:195] Run: sudo find /etc/cni/net.d -maxdepth 1 -type f ( ( -name *bridge* -or -name *podman* ) -and -not -name *.mk_disabled ) -printf "%p, " -exec sh -c "sudo mv {} {}.mk_disabled" ;
I1001 11:09:38.942103   17292 cni.go:262] disabled [/etc/cni/net.d/87-podman-bridge.conflist, /etc/cni/net.d/100-crio-bridge.conf] bridge cni config(s)
I1001 11:09:38.942103   17292 start.go:495] detecting cgroup driver to use...
I1001 11:09:38.942103   17292 detect.go:187] detected "cgroupfs" cgroup driver on host os
I1001 11:09:38.946606   17292 ssh_runner.go:195] Run: /bin/bash -c "sudo mkdir -p /etc && printf %s "runtime-endpoint: unix:///run/containerd/containerd.sock
" | sudo tee /etc/crictl.yaml"
I1001 11:09:38.999992   17292 ssh_runner.go:195] Run: sh -c "sudo sed -i -r 's|^( *)sandbox_image = .*$|\1sandbox_image = "registry.k8s.io/pause:3.10.1"|' /etc/containerd/config.toml"
I1001 11:09:39.033563   17292 ssh_runner.go:195] Run: sh -c "sudo sed -i -r 's|^( *)restrict_oom_score_adj = .*$|\1restrict_oom_score_adj = false|' /etc/containerd/config.toml"
I1001 11:09:39.071157   17292 containerd.go:146] configuring containerd to use "cgroupfs" as cgroup driver...
I1001 11:09:39.098536   17292 ssh_runner.go:195] Run: sh -c "sudo sed -i -r 's|^( *)SystemdCgroup = .*$|\1SystemdCgroup = false|g' /etc/containerd/config.toml"
I1001 11:09:39.141187   17292 ssh_runner.go:195] Run: sh -c "sudo sed -i 's|"io.containerd.runtime.v1.linux"|"io.containerd.runc.v2"|g' /etc/containerd/config.toml"
I1001 11:09:39.182657   17292 ssh_runner.go:195] Run: sh -c "sudo sed -i '/systemd_cgroup/d' /etc/containerd/config.toml"
I1001 11:09:39.271357   17292 ssh_runner.go:195] Run: sh -c "sudo sed -i 's|"io.containerd.runc.v1"|"io.containerd.runc.v2"|g' /etc/containerd/config.toml"
I1001 11:09:39.321535   17292 ssh_runner.go:195] Run: sh -c "sudo rm -rf /etc/cni/net.mk"
I1001 11:09:39.361227   17292 ssh_runner.go:195] Run: sh -c "sudo sed -i -r 's|^( *)conf_dir = .*$|\1conf_dir = "/etc/cni/net.d"|g' /etc/containerd/config.toml"
I1001 11:09:39.413510   17292 ssh_runner.go:195] Run: sh -c "sudo sed -i '/^ *enable_unprivileged_ports = .*/d' /etc/containerd/config.toml"
I1001 11:09:39.457317   17292 ssh_runner.go:195] Run: sh -c "sudo sed -i -r 's|^( *)\[plugins."io.containerd.grpc.v1.cri"\]|&\n\1  enable_unprivileged_ports = true|' /etc/containerd/config.toml"
I1001 11:09:39.476437   17292 ssh_runner.go:195] Run: sudo sysctl net.bridge.bridge-nf-call-iptables
I1001 11:09:39.526719   17292 ssh_runner.go:195] Run: sudo sh -c "echo 1 > /proc/sys/net/ipv4/ip_forward"
I1001 11:09:39.622467   17292 ssh_runner.go:195] Run: sudo systemctl daemon-reload
I1001 11:09:39.756136   17292 ssh_runner.go:195] Run: sudo systemctl restart containerd
I1001 11:09:39.872392   17292 start.go:495] detecting cgroup driver to use...
I1001 11:09:39.872392   17292 detect.go:187] detected "cgroupfs" cgroup driver on host os
I1001 11:09:39.875596   17292 ssh_runner.go:195] Run: sudo systemctl cat docker.service
I1001 11:09:39.894708   17292 ssh_runner.go:195] Run: sudo systemctl is-active --quiet service containerd
I1001 11:09:39.911427   17292 ssh_runner.go:195] Run: sudo systemctl stop -f containerd
I1001 11:09:39.934689   17292 ssh_runner.go:195] Run: sudo systemctl is-active --quiet service containerd
I1001 11:09:39.951138   17292 ssh_runner.go:195] Run: sudo systemctl is-active --quiet service crio
I1001 11:09:39.968690   17292 ssh_runner.go:195] Run: /bin/bash -c "sudo mkdir -p /etc && printf %s "runtime-endpoint: unix:///var/run/cri-dockerd.sock
" | sudo tee /etc/crictl.yaml"
I1001 11:09:40.010941   17292 ssh_runner.go:195] Run: which cri-dockerd
I1001 11:09:40.020938   17292 ssh_runner.go:195] Run: sudo mkdir -p /etc/systemd/system/cri-docker.service.d
I1001 11:09:40.034678   17292 ssh_runner.go:362] scp memory --> /etc/systemd/system/cri-docker.service.d/10-cni.conf (192 bytes)
I1001 11:09:40.075431   17292 ssh_runner.go:195] Run: sudo systemctl unmask docker.service
I1001 11:09:40.208863   17292 ssh_runner.go:195] Run: sudo systemctl enable docker.socket
I1001 11:09:40.309062   17292 docker.go:575] configuring docker to use "cgroupfs" as cgroup driver...
I1001 11:09:40.309062   17292 ssh_runner.go:362] scp memory --> /etc/docker/daemon.json (130 bytes)
I1001 11:09:40.340788   17292 ssh_runner.go:195] Run: sudo systemctl reset-failed docker
I1001 11:09:40.363012   17292 ssh_runner.go:195] Run: sudo systemctl daemon-reload
I1001 11:09:40.455799   17292 ssh_runner.go:195] Run: sudo systemctl restart docker
I1001 11:09:42.068564   17292 ssh_runner.go:235] Completed: sudo systemctl restart docker: (1.6127644s)
I1001 11:09:42.071954   17292 ssh_runner.go:195] Run: sudo systemctl is-active --quiet service docker
I1001 11:09:42.086676   17292 ssh_runner.go:195] Run: sudo systemctl is-active --quiet service cri-docker.socket
I1001 11:09:42.105181   17292 ssh_runner.go:195] Run: sudo systemctl is-active --quiet service cri-docker.service
I1001 11:09:42.119951   17292 ssh_runner.go:195] Run: sudo systemctl unmask cri-docker.socket
I1001 11:09:42.203628   17292 ssh_runner.go:195] Run: sudo systemctl enable cri-docker.socket
I1001 11:09:42.298607   17292 ssh_runner.go:195] Run: sudo systemctl daemon-reload
W1001 11:09:42.309863   17292 out.go:285] ❗  Failing to connect to https://registry.k8s.io/ from inside the minikube container
W1001 11:09:42.309863   17292 out.go:285] 💡  To pull new external images, you may need to configure a proxy: https://minikube.sigs.k8s.io/docs/reference/networking/proxy/
I1001 11:09:42.395939   17292 ssh_runner.go:195] Run: sudo systemctl restart cri-docker.socket
I1001 11:09:42.440309   17292 ssh_runner.go:195] Run: sudo systemctl reset-failed cri-docker.service
I1001 11:09:42.485123   17292 ssh_runner.go:195] Run: sudo systemctl daemon-reload
I1001 11:09:42.720832   17292 ssh_runner.go:195] Run: sudo systemctl restart cri-docker.service
I1001 11:09:42.839056   17292 ssh_runner.go:195] Run: sudo systemctl is-active --quiet service cri-docker.service
I1001 11:09:42.854908   17292 start.go:542] Will wait 60s for socket path /var/run/cri-dockerd.sock
I1001 11:09:42.867338   17292 ssh_runner.go:195] Run: stat /var/run/cri-dockerd.sock
I1001 11:09:42.874286   17292 start.go:563] Will wait 60s for crictl version
I1001 11:09:42.891434   17292 ssh_runner.go:195] Run: which crictl
I1001 11:09:42.899698   17292 ssh_runner.go:195] Run: sudo /usr/bin/crictl version
I1001 11:09:42.971242   17292 start.go:579] Version:  0.1.0
RuntimeName:  docker
RuntimeVersion:  28.4.0
RuntimeApiVersion:  v1
I1001 11:09:42.983839   17292 ssh_runner.go:195] Run: docker version --format {{.Server.Version}}
I1001 11:09:43.024550   17292 ssh_runner.go:195] Run: docker version --format {{.Server.Version}}
I1001 11:09:43.058695   17292 out.go:252] 🐳  Preparing Kubernetes v1.34.0 on Docker 28.4.0 ...
I1001 11:09:43.070426   17292 cli_runner.go:164] Run: docker exec -t minikube dig +short host.docker.internal
I1001 11:09:43.268576   17292 network.go:96] got host ip for mount in container by digging dns: **************
I1001 11:09:43.291834   17292 ssh_runner.go:195] Run: grep **************	host.minikube.internal$ /etc/hosts
I1001 11:09:43.298210   17292 ssh_runner.go:195] Run: /bin/bash -c "{ grep -v $'\thost.minikube.internal$' "/etc/hosts"; echo "**************	host.minikube.internal"; } > /tmp/h.$$; sudo cp /tmp/h.$$ "/etc/hosts""
I1001 11:09:43.326286   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "8443/tcp") 0).HostPort}}'" minikube
I1001 11:09:43.410346   17292 kubeadm.go:875] updating cluster {Name:minikube KeepContext:false EmbedCerts:false MinikubeISO: KicBaseImage:gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 Memory:4000 CPUs:2 DiskSize:20000 Driver:docker HyperkitVpnKitSock: HyperkitVSockPorts:[] DockerEnv:[] ContainerVolumeMounts:[] InsecureRegistry:[] RegistryMirror:[] HostOnlyCIDR:************/24 HypervVirtualSwitch: HypervUseExternalSwitch:false HypervExternalAdapter: KVMNetwork:default KVMQemuURI:qemu:///system KVMGPU:false KVMHidden:false KVMNUMACount:1 APIServerPort:8443 DockerOpt:[] DisableDriverMounts:false NFSShare:[] NFSSharesRoot:/nfsshares UUID: NoVTXCheck:false DNSProxy:false HostDNSResolver:true HostOnlyNicType:virtio NatNicType:virtio SSHIPAddress: SSHUser:root SSHKey: SSHPort:22 KubernetesConfig:{KubernetesVersion:v1.34.0 ClusterName:minikube Namespace:default APIServerHAVIP: APIServerName:minikubeCA APIServerNames:[] APIServerIPs:[] DNSDomain:cluster.local ContainerRuntime:docker CRISocket: NetworkPlugin:cni FeatureGates: ServiceCIDR:*********/12 ImageRepository: LoadBalancerStartIP: LoadBalancerEndIP: CustomIngressCert: RegistryAliases: ExtraOptions:[] ShouldLoadCachedImages:true EnableDefaultCNI:false CNI:} Nodes:[{Name: IP:************ Port:8443 KubernetesVersion:v1.34.0 ContainerRuntime:docker ControlPlane:true Worker:true}] Addons:map[] CustomAddonImages:map[] CustomAddonRegistries:map[] VerifyComponents:map[apiserver:true system_pods:true] StartHostTimeout:6m0s ScheduledStop:<nil> ExposedPorts:[] ListenAddress: Network: Subnet: MultiNodeRequested:false ExtraDisks:0 CertExpiration:26280h0m0s MountString: Mount9PVersion:9p2000.L MountGID:docker MountIP: MountMSize:262144 MountOptions:[] MountPort:0 MountType:9p MountUID:docker BinaryMirror: DisableOptimizations:false DisableMetrics:false DisableCoreDNSLog:false CustomQemuFirmwarePath: SocketVMnetClientPath: SocketVMnetPath: StaticIP: SSHAuthSock: SSHAgentPID:0 GPUs: AutoPauseInterval:1m0s} ...
I1001 11:09:43.410346   17292 preload.go:131] Checking if preload exists for k8s version v1.34.0 and runtime docker
I1001 11:09:43.420950   17292 ssh_runner.go:195] Run: docker images --format {{.Repository}}:{{.Tag}}
I1001 11:09:43.453209   17292 docker.go:691] Got preloaded images: 
I1001 11:09:43.453209   17292 docker.go:697] registry.k8s.io/kube-apiserver:v1.34.0 wasn't preloaded
I1001 11:09:43.454918   17292 ssh_runner.go:195] Run: sudo cat /var/lib/docker/image/overlay2/repositories.json
I1001 11:09:43.480865   17292 ssh_runner.go:195] Run: which lz4
W1001 11:09:43.488775   17292 vm_assets.go:172] NewFileAsset: C:\Users\<USER>\.minikube\cache\preloaded-tarball\preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4 is an empty file!
I1001 11:09:43.489357   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\cache\preloaded-tarball\preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4 --> /preloaded.tar.lz4 (0 bytes)
W1001 11:09:43.489357   17292 ssh_runner.go:364] 0 byte asset: &{BaseAsset:{SourcePath:C:\Users\<USER>\.minikube\cache\preloaded-tarball\preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4 TargetDir:/ TargetName:preloaded.tar.lz4 Permissions:0644 Source:} reader:0xc00174f6b0 writer:<nil> file:0xc000114510}
W1001 11:09:43.492259   17292 ssh_runner.go:391] asked to copy a 0 byte asset: &{BaseAsset:{SourcePath:C:\Users\<USER>\.minikube\cache\preloaded-tarball\preloaded-images-k8s-v18-v1.34.0-docker-overlay2-amd64.tar.lz4 TargetDir:/ TargetName:preloaded.tar.lz4 Permissions:0644 Source:} reader:0xc00174f6b0 writer:<nil> file:0xc000114510}
I1001 11:09:43.516686   17292 docker.go:655] duration metric: took 27.9109ms to copy over tarball
I1001 11:09:43.517791   17292 ssh_runner.go:195] Run: sudo tar --xattrs --xattrs-include security.capability -I lz4 -C /var -xf /preloaded.tar.lz4
I1001 11:09:43.530975   17292 ssh_runner.go:146] rm: /preloaded.tar.lz4
I1001 11:09:43.544091   17292 ssh_runner.go:195] Run: sudo cat /var/lib/docker/image/overlay2/repositories.json
I1001 11:09:43.557238   17292 ssh_runner.go:362] scp memory --> /var/lib/docker/image/overlay2/repositories.json (19 bytes)
I1001 11:09:43.578815   17292 ssh_runner.go:195] Run: sudo systemctl reset-failed docker
I1001 11:09:43.595190   17292 ssh_runner.go:195] Run: sudo systemctl daemon-reload
I1001 11:09:43.681000   17292 ssh_runner.go:195] Run: sudo systemctl restart docker
I1001 11:09:45.049540   17292 ssh_runner.go:235] Completed: sudo systemctl restart docker: (1.3685399s)
I1001 11:09:45.056793   17292 ssh_runner.go:195] Run: docker images --format {{.Repository}}:{{.Tag}}
I1001 11:09:45.075742   17292 docker.go:691] Got preloaded images: 
I1001 11:09:45.075742   17292 docker.go:697] registry.k8s.io/kube-apiserver:v1.34.0 wasn't preloaded
I1001 11:09:45.075742   17292 cache_images.go:89] LoadCachedImages start: [registry.k8s.io/kube-apiserver:v1.34.0 registry.k8s.io/kube-controller-manager:v1.34.0 registry.k8s.io/kube-scheduler:v1.34.0 registry.k8s.io/kube-proxy:v1.34.0 registry.k8s.io/pause:3.10.1 registry.k8s.io/etcd:3.6.4-0 registry.k8s.io/coredns/coredns:v1.12.1 gcr.io/k8s-minikube/storage-provisioner:v5]
I1001 11:09:45.200433   17292 image.go:138] retrieving image: gcr.io/k8s-minikube/storage-provisioner:v5
I1001 11:09:45.207119   17292 image.go:138] retrieving image: registry.k8s.io/kube-proxy:v1.34.0
I1001 11:09:45.213790   17292 image.go:138] retrieving image: registry.k8s.io/kube-apiserver:v1.34.0
I1001 11:09:45.216198   17292 image.go:181] daemon lookup for gcr.io/k8s-minikube/storage-provisioner:v5: Error response from daemon: No such image: gcr.io/k8s-minikube/storage-provisioner:v5
I1001 11:09:45.222064   17292 image.go:138] retrieving image: registry.k8s.io/kube-controller-manager:v1.34.0
I1001 11:09:45.224201   17292 image.go:181] daemon lookup for registry.k8s.io/kube-proxy:v1.34.0: Error response from daemon: No such image: registry.k8s.io/kube-proxy:v1.34.0
I1001 11:09:45.230587   17292 image.go:138] retrieving image: registry.k8s.io/kube-scheduler:v1.34.0
I1001 11:09:45.264004   17292 image.go:138] retrieving image: registry.k8s.io/etcd:3.6.4-0
I1001 11:09:45.264639   17292 image.go:181] daemon lookup for registry.k8s.io/kube-controller-manager:v1.34.0: Error response from daemon: No such image: registry.k8s.io/kube-controller-manager:v1.34.0
I1001 11:09:45.264639   17292 image.go:181] daemon lookup for registry.k8s.io/kube-apiserver:v1.34.0: Error response from daemon: No such image: registry.k8s.io/kube-apiserver:v1.34.0
I1001 11:09:45.277368   17292 image.go:138] retrieving image: registry.k8s.io/pause:3.10.1
I1001 11:09:45.302686   17292 image.go:138] retrieving image: registry.k8s.io/coredns/coredns:v1.12.1
I1001 11:09:45.302686   17292 image.go:181] daemon lookup for registry.k8s.io/kube-scheduler:v1.34.0: Error response from daemon: No such image: registry.k8s.io/kube-scheduler:v1.34.0
I1001 11:09:45.309950   17292 image.go:181] daemon lookup for registry.k8s.io/pause:3.10.1: Error response from daemon: No such image: registry.k8s.io/pause:3.10.1
I1001 11:09:45.309950   17292 image.go:181] daemon lookup for registry.k8s.io/etcd:3.6.4-0: Error response from daemon: No such image: registry.k8s.io/etcd:3.6.4-0
I1001 11:09:45.321130   17292 image.go:181] daemon lookup for registry.k8s.io/coredns/coredns:v1.12.1: Error response from daemon: No such image: registry.k8s.io/coredns/coredns:v1.12.1
I1001 11:09:46.144704   17292 ssh_runner.go:195] Run: docker image inspect --format {{.Id}} registry.k8s.io/kube-proxy:v1.34.0
I1001 11:09:46.171278   17292 cache_images.go:117] "registry.k8s.io/kube-proxy:v1.34.0" needs transfer: "registry.k8s.io/kube-proxy:v1.34.0" does not exist at hash "df0860106674df871eebbd01fede90c764bf472f5b97eca7e945761292e9b0ce" in container runtime
I1001 11:09:46.171278   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-proxy:v1.34.0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-proxy_v1.34.0
I1001 11:09:46.176824   17292 docker.go:338] Removing image: registry.k8s.io/kube-proxy:v1.34.0
I1001 11:09:46.182603   17292 ssh_runner.go:195] Run: docker rmi registry.k8s.io/kube-proxy:v1.34.0
I1001 11:09:46.200601   17292 cache_images.go:290] Loading image from: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-proxy_v1.34.0
I1001 11:09:46.207712   17292 ssh_runner.go:195] Run: docker image inspect --format {{.Id}} registry.k8s.io/kube-controller-manager:v1.34.0
I1001 11:09:46.214726   17292 ssh_runner.go:195] Run: stat -c "%s %y" /var/lib/minikube/images/kube-proxy_v1.34.0
I1001 11:09:46.227266   17292 ssh_runner.go:352] existence check for /var/lib/minikube/images/kube-proxy_v1.34.0: stat -c "%s %y" /var/lib/minikube/images/kube-proxy_v1.34.0: Process exited with status 1
stdout:

stderr:
stat: cannot statx '/var/lib/minikube/images/kube-proxy_v1.34.0': No such file or directory
I1001 11:09:46.227266   17292 cache_images.go:117] "registry.k8s.io/kube-controller-manager:v1.34.0" needs transfer: "registry.k8s.io/kube-controller-manager:v1.34.0" does not exist at hash "a0af72f2ec6d628152b015a46d4074df8f77d5b686978987c70f48b8c7660634" in container runtime
I1001 11:09:46.227266   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-controller-manager:v1.34.0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-controller-manager_v1.34.0
I1001 11:09:46.227266   17292 docker.go:338] Removing image: registry.k8s.io/kube-controller-manager:v1.34.0
I1001 11:09:46.227266   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-proxy_v1.34.0 --> /var/lib/minikube/images/kube-proxy_v1.34.0 (25966080 bytes)
I1001 11:09:46.238232   17292 ssh_runner.go:195] Run: docker rmi registry.k8s.io/kube-controller-manager:v1.34.0
I1001 11:09:46.309313   17292 ssh_runner.go:195] Run: docker image inspect --format {{.Id}} registry.k8s.io/kube-apiserver:v1.34.0
I1001 11:09:46.322339   17292 cache_images.go:290] Loading image from: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-controller-manager_v1.34.0
I1001 11:09:46.341378   17292 ssh_runner.go:195] Run: stat -c "%s %y" /var/lib/minikube/images/kube-controller-manager_v1.34.0
I1001 11:09:46.376570   17292 cache_images.go:117] "registry.k8s.io/kube-apiserver:v1.34.0" needs transfer: "registry.k8s.io/kube-apiserver:v1.34.0" does not exist at hash "90550c43ad2bcfd11fcd5fd27d2eac5a7ca823be1308884b33dd816ec169be90" in container runtime
I1001 11:09:46.376570   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-apiserver:v1.34.0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-apiserver_v1.34.0
I1001 11:09:46.376570   17292 docker.go:338] Removing image: registry.k8s.io/kube-apiserver:v1.34.0
I1001 11:09:46.384778   17292 ssh_runner.go:195] Run: docker rmi registry.k8s.io/kube-apiserver:v1.34.0
I1001 11:09:46.392511   17292 ssh_runner.go:352] existence check for /var/lib/minikube/images/kube-controller-manager_v1.34.0: stat -c "%s %y" /var/lib/minikube/images/kube-controller-manager_v1.34.0: Process exited with status 1
stdout:

stderr:
stat: cannot statx '/var/lib/minikube/images/kube-controller-manager_v1.34.0': No such file or directory
I1001 11:09:46.392511   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-controller-manager_v1.34.0 --> /var/lib/minikube/images/kube-controller-manager_v1.34.0 (22830592 bytes)
I1001 11:09:46.412056   17292 ssh_runner.go:195] Run: docker image inspect --format {{.Id}} registry.k8s.io/kube-scheduler:v1.34.0
I1001 11:09:46.457029   17292 cache_images.go:290] Loading image from: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-apiserver_v1.34.0
I1001 11:09:46.473994   17292 ssh_runner.go:195] Run: stat -c "%s %y" /var/lib/minikube/images/kube-apiserver_v1.34.0
I1001 11:09:46.483938   17292 cache_images.go:117] "registry.k8s.io/kube-scheduler:v1.34.0" needs transfer: "registry.k8s.io/kube-scheduler:v1.34.0" does not exist at hash "46169d968e9203e8b10debaf898210fe11c94b5864c351ea0f6fcf621f659bdc" in container runtime
I1001 11:09:46.483938   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-scheduler:v1.34.0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-scheduler_v1.34.0
I1001 11:09:46.483938   17292 docker.go:338] Removing image: registry.k8s.io/kube-scheduler:v1.34.0
I1001 11:09:46.489886   17292 ssh_runner.go:195] Run: docker image inspect --format {{.Id}} registry.k8s.io/pause:3.10.1
I1001 11:09:46.493615   17292 ssh_runner.go:195] Run: docker rmi registry.k8s.io/kube-scheduler:v1.34.0
I1001 11:09:46.504717   17292 ssh_runner.go:352] existence check for /var/lib/minikube/images/kube-apiserver_v1.34.0: stat -c "%s %y" /var/lib/minikube/images/kube-apiserver_v1.34.0: Process exited with status 1
stdout:

stderr:
stat: cannot statx '/var/lib/minikube/images/kube-apiserver_v1.34.0': No such file or directory
I1001 11:09:46.505240   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-apiserver_v1.34.0 --> /var/lib/minikube/images/kube-apiserver_v1.34.0 (27077120 bytes)
I1001 11:09:46.588631   17292 cache_images.go:117] "registry.k8s.io/pause:3.10.1" needs transfer: "registry.k8s.io/pause:3.10.1" does not exist at hash "cd073f4c5f6a8e9dc6f3125ba00cf60819cae95c1ec84a1f146ee4a9cf9e803f" in container runtime
I1001 11:09:46.588631   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\pause:3.10.1 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\pause_3.10.1
I1001 11:09:46.588631   17292 docker.go:338] Removing image: registry.k8s.io/pause:3.10.1
I1001 11:09:46.599153   17292 cache_images.go:290] Loading image from: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-scheduler_v1.34.0
I1001 11:09:46.599912   17292 ssh_runner.go:195] Run: docker rmi registry.k8s.io/pause:3.10.1
I1001 11:09:46.617434   17292 ssh_runner.go:195] Run: stat -c "%s %y" /var/lib/minikube/images/kube-scheduler_v1.34.0
I1001 11:09:46.644748   17292 ssh_runner.go:195] Run: docker image inspect --format {{.Id}} registry.k8s.io/etcd:3.6.4-0
I1001 11:09:46.705908   17292 ssh_runner.go:195] Run: docker image inspect --format {{.Id}} registry.k8s.io/coredns/coredns:v1.12.1
I1001 11:09:46.712213   17292 ssh_runner.go:352] existence check for /var/lib/minikube/images/kube-scheduler_v1.34.0: stat -c "%s %y" /var/lib/minikube/images/kube-scheduler_v1.34.0: Process exited with status 1
stdout:

stderr:
stat: cannot statx '/var/lib/minikube/images/kube-scheduler_v1.34.0': No such file or directory
I1001 11:09:46.712747   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-scheduler_v1.34.0 --> /var/lib/minikube/images/kube-scheduler_v1.34.0 (17396736 bytes)
I1001 11:09:46.713809   17292 cache_images.go:290] Loading image from: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\pause_3.10.1
I1001 11:09:46.733514   17292 ssh_runner.go:195] Run: stat -c "%s %y" /var/lib/minikube/images/pause_3.10.1
I1001 11:09:46.762047   17292 cache_images.go:117] "registry.k8s.io/etcd:3.6.4-0" needs transfer: "registry.k8s.io/etcd:3.6.4-0" does not exist at hash "5f1f5298c888daa46c4409ff4cefe5ca9d16e479419f94cdb5f5d5563dac0115" in container runtime
I1001 11:09:46.762047   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\etcd:3.6.4-0 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\etcd_3.6.4-0
I1001 11:09:46.762047   17292 docker.go:338] Removing image: registry.k8s.io/etcd:3.6.4-0
I1001 11:09:46.771846   17292 ssh_runner.go:195] Run: docker rmi registry.k8s.io/etcd:3.6.4-0
I1001 11:09:46.806513   17292 ssh_runner.go:352] existence check for /var/lib/minikube/images/pause_3.10.1: stat -c "%s %y" /var/lib/minikube/images/pause_3.10.1: Process exited with status 1
stdout:

stderr:
stat: cannot statx '/var/lib/minikube/images/pause_3.10.1': No such file or directory
I1001 11:09:46.807037   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\pause_3.10.1 --> /var/lib/minikube/images/pause_3.10.1 (321024 bytes)
I1001 11:09:46.810608   17292 cache_images.go:117] "registry.k8s.io/coredns/coredns:v1.12.1" needs transfer: "registry.k8s.io/coredns/coredns:v1.12.1" does not exist at hash "52546a367cc9e0d924aa3b190596a9167fa6e53245023b5b5baf0f07e5443969" in container runtime
I1001 11:09:46.810608   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\coredns\coredns:v1.12.1 -> C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\coredns\coredns_v1.12.1
I1001 11:09:46.811113   17292 docker.go:338] Removing image: registry.k8s.io/coredns/coredns:v1.12.1
I1001 11:09:46.820700   17292 ssh_runner.go:195] Run: docker rmi registry.k8s.io/coredns/coredns:v1.12.1
I1001 11:09:46.871119   17292 cache_images.go:290] Loading image from: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\etcd_3.6.4-0
I1001 11:09:46.893984   17292 ssh_runner.go:195] Run: stat -c "%s %y" /var/lib/minikube/images/etcd_3.6.4-0
I1001 11:09:46.912213   17292 cache_images.go:290] Loading image from: C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\coredns\coredns_v1.12.1
I1001 11:09:46.936171   17292 docker.go:305] Loading image: /var/lib/minikube/images/pause_3.10.1
I1001 11:09:46.936171   17292 ssh_runner.go:195] Run: /bin/bash -c "sudo cat /var/lib/minikube/images/pause_3.10.1 | docker load"
I1001 11:09:46.936486   17292 ssh_runner.go:195] Run: stat -c "%s %y" /var/lib/minikube/images/coredns_v1.12.1
I1001 11:09:46.945587   17292 ssh_runner.go:352] existence check for /var/lib/minikube/images/etcd_3.6.4-0: stat -c "%s %y" /var/lib/minikube/images/etcd_3.6.4-0: Process exited with status 1
stdout:

stderr:
stat: cannot statx '/var/lib/minikube/images/etcd_3.6.4-0': No such file or directory
I1001 11:09:46.945587   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\etcd_3.6.4-0 --> /var/lib/minikube/images/etcd_3.6.4-0 (74320896 bytes)
I1001 11:09:47.156885   17292 ssh_runner.go:352] existence check for /var/lib/minikube/images/coredns_v1.12.1: stat -c "%s %y" /var/lib/minikube/images/coredns_v1.12.1: Process exited with status 1
stdout:

stderr:
stat: cannot statx '/var/lib/minikube/images/coredns_v1.12.1': No such file or directory
I1001 11:09:47.157411   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\coredns\coredns_v1.12.1 --> /var/lib/minikube/images/coredns_v1.12.1 (22394368 bytes)
I1001 11:09:47.160028   17292 cache_images.go:322] Transferred and loaded C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\pause_3.10.1 from cache
I1001 11:09:47.749833   17292 ssh_runner.go:195] Run: docker image inspect --format {{.Id}} gcr.io/k8s-minikube/storage-provisioner:v5
I1001 11:09:47.872288   17292 cache_images.go:117] "gcr.io/k8s-minikube/storage-provisioner:v5" needs transfer: "gcr.io/k8s-minikube/storage-provisioner:v5" does not exist at hash "6e38f40d628db3002f5617342c8872c935de530d867d0f709a2fbda1a302a562" in container runtime
I1001 11:09:47.872814   17292 localpath.go:148] windows sanitize: C:\Users\<USER>\.minikube\cache\images\amd64\gcr.io\k8s-minikube\storage-provisioner:v5 -> C:\Users\<USER>\.minikube\cache\images\amd64\gcr.io\k8s-minikube\storage-provisioner_v5
I1001 11:09:47.872814   17292 docker.go:338] Removing image: gcr.io/k8s-minikube/storage-provisioner:v5
I1001 11:09:47.883412   17292 ssh_runner.go:195] Run: docker rmi gcr.io/k8s-minikube/storage-provisioner:v5
I1001 11:09:47.955807   17292 cache_images.go:290] Loading image from: C:\Users\<USER>\.minikube\cache\images\amd64\gcr.io\k8s-minikube\storage-provisioner_v5
I1001 11:09:47.976937   17292 ssh_runner.go:195] Run: stat -c "%s %y" /var/lib/minikube/images/storage-provisioner_v5
I1001 11:09:48.028284   17292 ssh_runner.go:352] existence check for /var/lib/minikube/images/storage-provisioner_v5: stat -c "%s %y" /var/lib/minikube/images/storage-provisioner_v5: Process exited with status 1
stdout:

stderr:
stat: cannot statx '/var/lib/minikube/images/storage-provisioner_v5': No such file or directory
I1001 11:09:48.028808   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\cache\images\amd64\gcr.io\k8s-minikube\storage-provisioner_v5 --> /var/lib/minikube/images/storage-provisioner_v5 (9060352 bytes)
I1001 11:09:48.898098   17292 docker.go:305] Loading image: /var/lib/minikube/images/kube-proxy_v1.34.0
I1001 11:09:48.898098   17292 ssh_runner.go:195] Run: /bin/bash -c "sudo cat /var/lib/minikube/images/kube-proxy_v1.34.0 | docker load"
I1001 11:09:51.576933   17292 ssh_runner.go:235] Completed: /bin/bash -c "sudo cat /var/lib/minikube/images/kube-proxy_v1.34.0 | docker load": (2.6788348s)
I1001 11:09:51.576933   17292 cache_images.go:322] Transferred and loaded C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-proxy_v1.34.0 from cache
I1001 11:09:51.576933   17292 docker.go:305] Loading image: /var/lib/minikube/images/kube-controller-manager_v1.34.0
I1001 11:09:51.576933   17292 ssh_runner.go:195] Run: /bin/bash -c "sudo cat /var/lib/minikube/images/kube-controller-manager_v1.34.0 | docker load"
I1001 11:09:53.054593   17292 ssh_runner.go:235] Completed: /bin/bash -c "sudo cat /var/lib/minikube/images/kube-controller-manager_v1.34.0 | docker load": (1.4776602s)
I1001 11:09:53.054593   17292 cache_images.go:322] Transferred and loaded C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-controller-manager_v1.34.0 from cache
I1001 11:09:53.054593   17292 docker.go:305] Loading image: /var/lib/minikube/images/kube-scheduler_v1.34.0
I1001 11:09:53.054593   17292 ssh_runner.go:195] Run: /bin/bash -c "sudo cat /var/lib/minikube/images/kube-scheduler_v1.34.0 | docker load"
I1001 11:09:53.470087   17292 cache_images.go:322] Transferred and loaded C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-scheduler_v1.34.0 from cache
I1001 11:09:53.470087   17292 docker.go:305] Loading image: /var/lib/minikube/images/storage-provisioner_v5
I1001 11:09:53.470087   17292 ssh_runner.go:195] Run: /bin/bash -c "sudo cat /var/lib/minikube/images/storage-provisioner_v5 | docker load"
I1001 11:09:53.736397   17292 cache_images.go:322] Transferred and loaded C:\Users\<USER>\.minikube\cache\images\amd64\gcr.io\k8s-minikube\storage-provisioner_v5 from cache
I1001 11:09:53.737029   17292 docker.go:305] Loading image: /var/lib/minikube/images/kube-apiserver_v1.34.0
I1001 11:09:53.737029   17292 ssh_runner.go:195] Run: /bin/bash -c "sudo cat /var/lib/minikube/images/kube-apiserver_v1.34.0 | docker load"
I1001 11:09:54.616739   17292 cache_images.go:322] Transferred and loaded C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\kube-apiserver_v1.34.0 from cache
I1001 11:09:54.616739   17292 docker.go:305] Loading image: /var/lib/minikube/images/coredns_v1.12.1
I1001 11:09:54.616739   17292 ssh_runner.go:195] Run: /bin/bash -c "sudo cat /var/lib/minikube/images/coredns_v1.12.1 | docker load"
I1001 11:09:56.216601   17292 ssh_runner.go:235] Completed: /bin/bash -c "sudo cat /var/lib/minikube/images/coredns_v1.12.1 | docker load": (1.5998612s)
I1001 11:09:56.216601   17292 cache_images.go:322] Transferred and loaded C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\coredns\coredns_v1.12.1 from cache
I1001 11:09:56.216601   17292 docker.go:305] Loading image: /var/lib/minikube/images/etcd_3.6.4-0
I1001 11:09:56.216601   17292 ssh_runner.go:195] Run: /bin/bash -c "sudo cat /var/lib/minikube/images/etcd_3.6.4-0 | docker load"
I1001 11:09:58.063864   17292 ssh_runner.go:235] Completed: /bin/bash -c "sudo cat /var/lib/minikube/images/etcd_3.6.4-0 | docker load": (1.847263s)
I1001 11:09:58.063864   17292 cache_images.go:322] Transferred and loaded C:\Users\<USER>\.minikube\cache\images\amd64\registry.k8s.io\etcd_3.6.4-0 from cache
I1001 11:09:58.063864   17292 cache_images.go:124] Successfully loaded all cached images
I1001 11:09:58.063864   17292 cache_images.go:93] duration metric: took 12.9881217s to LoadCachedImages
I1001 11:09:58.063864   17292 kubeadm.go:926] updating node { ************ 8443 v1.34.0 docker true true} ...
I1001 11:09:58.473649   17292 kubeadm.go:938] kubelet [Unit]
Wants=docker.socket

[Service]
ExecStart=
ExecStart=/var/lib/minikube/binaries/v1.34.0/kubelet --bootstrap-kubeconfig=/etc/kubernetes/bootstrap-kubelet.conf --config=/var/lib/kubelet/config.yaml --hostname-override=minikube --kubeconfig=/etc/kubernetes/kubelet.conf --node-ip=************

[Install]
 config:
{KubernetesVersion:v1.34.0 ClusterName:minikube Namespace:default APIServerHAVIP: APIServerName:minikubeCA APIServerNames:[] APIServerIPs:[] DNSDomain:cluster.local ContainerRuntime:docker CRISocket: NetworkPlugin:cni FeatureGates: ServiceCIDR:*********/12 ImageRepository: LoadBalancerStartIP: LoadBalancerEndIP: CustomIngressCert: RegistryAliases: ExtraOptions:[] ShouldLoadCachedImages:true EnableDefaultCNI:false CNI:}
I1001 11:09:58.486335   17292 ssh_runner.go:195] Run: docker info --format {{.CgroupDriver}}
I1001 11:09:58.543424   17292 cni.go:84] Creating CNI manager for ""
I1001 11:09:58.543424   17292 cni.go:158] "docker" driver + "docker" container runtime found on kubernetes v1.24+, recommending bridge
I1001 11:09:58.543989   17292 kubeadm.go:84] Using pod CIDR: **********/16
I1001 11:09:58.543989   17292 kubeadm.go:189] kubeadm options: {CertDir:/var/lib/minikube/certs ServiceCIDR:*********/12 PodSubnet:**********/16 AdvertiseAddress:************ APIServerPort:8443 KubernetesVersion:v1.34.0 EtcdDataDir:/var/lib/minikube/etcd EtcdExtraArgs:map[] ClusterName:minikube NodeName:minikube DNSDomain:cluster.local CRISocket:/var/run/cri-dockerd.sock ImageRepository: ComponentOptions:[{Component:apiServer ExtraArgs:map[enable-admission-plugins:NamespaceLifecycle,LimitRanger,ServiceAccount,DefaultStorageClass,DefaultTolerationSeconds,NodeRestriction,MutatingAdmissionWebhook,ValidatingAdmissionWebhook,ResourceQuota] Pairs:map[certSANs:["127.0.0.1", "localhost", "************"]]} {Component:controllerManager ExtraArgs:map[allocate-node-cidrs:true leader-elect:false] Pairs:map[]} {Component:scheduler ExtraArgs:map[leader-elect:false] Pairs:map[]}] FeatureArgs:map[] NodeIP:************ CgroupDriver:cgroupfs ClientCAFile:/var/lib/minikube/certs/ca.crt StaticPodPath:/etc/kubernetes/manifests ControlPlaneAddress:control-plane.minikube.internal KubeProxyOptions:map[] ResolvConfSearchRegression:false KubeletConfigOpts:map[containerRuntimeEndpoint:unix:///var/run/cri-dockerd.sock hairpinMode:hairpin-veth runtimeRequestTimeout:15m] PrependCriSocketUnix:true}
I1001 11:09:58.543989   17292 kubeadm.go:195] kubeadm config:
apiVersion: kubeadm.k8s.io/v1beta4
kind: InitConfiguration
localAPIEndpoint:
  advertiseAddress: ************
  bindPort: 8443
bootstrapTokens:
  - groups:
      - system:bootstrappers:kubeadm:default-node-token
    ttl: 24h0m0s
    usages:
      - signing
      - authentication
nodeRegistration:
  criSocket: unix:///var/run/cri-dockerd.sock
  name: "minikube"
  kubeletExtraArgs:
    - name: "node-ip"
      value: "************"
  taints: []
---
apiVersion: kubeadm.k8s.io/v1beta4
kind: ClusterConfiguration
apiServer:
  certSANs: ["127.0.0.1", "localhost", "************"]
  extraArgs:
    - name: "enable-admission-plugins"
      value: "NamespaceLifecycle,LimitRanger,ServiceAccount,DefaultStorageClass,DefaultTolerationSeconds,NodeRestriction,MutatingAdmissionWebhook,ValidatingAdmissionWebhook,ResourceQuota"
controllerManager:
  extraArgs:
    - name: "allocate-node-cidrs"
      value: "true"
    - name: "leader-elect"
      value: "false"
scheduler:
  extraArgs:
    - name: "leader-elect"
      value: "false"
certificatesDir: /var/lib/minikube/certs
clusterName: mk
controlPlaneEndpoint: control-plane.minikube.internal:8443
etcd:
  local:
    dataDir: /var/lib/minikube/etcd
kubernetesVersion: v1.34.0
networking:
  dnsDomain: cluster.local
  podSubnet: "**********/16"
  serviceSubnet: *********/12
---
apiVersion: kubelet.config.k8s.io/v1beta1
kind: KubeletConfiguration
authentication:
  x509:
    clientCAFile: /var/lib/minikube/certs/ca.crt
cgroupDriver: cgroupfs
containerRuntimeEndpoint: unix:///var/run/cri-dockerd.sock
hairpinMode: hairpin-veth
runtimeRequestTimeout: 15m
clusterDomain: "cluster.local"
# disable disk resource management by default
imageGCHighThresholdPercent: 100
evictionHard:
  nodefs.available: "0%"
  nodefs.inodesFree: "0%"
  imagefs.available: "0%"
failSwapOn: false
staticPodPath: /etc/kubernetes/manifests
---
apiVersion: kubeproxy.config.k8s.io/v1alpha1
kind: KubeProxyConfiguration
clusterCIDR: "**********/16"
metricsBindAddress: 0.0.0.0:10249
conntrack:
  maxPerCore: 0
# Skip setting "net.netfilter.nf_conntrack_tcp_timeout_established"
  tcpEstablishedTimeout: 0s
# Skip setting "net.netfilter.nf_conntrack_tcp_timeout_close"
  tcpCloseWaitTimeout: 0s

I1001 11:09:58.546326   17292 ssh_runner.go:195] Run: sudo ls /var/lib/minikube/binaries/v1.34.0
I1001 11:09:58.559026   17292 binaries.go:47] Didn't find k8s binaries: sudo ls /var/lib/minikube/binaries/v1.34.0: Process exited with status 2
stdout:

stderr:
ls: cannot access '/var/lib/minikube/binaries/v1.34.0': No such file or directory

Initiating transfer...
I1001 11:09:58.560587   17292 ssh_runner.go:195] Run: sudo mkdir -p /var/lib/minikube/binaries/v1.34.0
I1001 11:09:58.575030   17292 download.go:108] Downloading: https://dl.k8s.io/release/v1.34.0/bin/linux/amd64/kubectl?checksum=file:https://dl.k8s.io/release/v1.34.0/bin/linux/amd64/kubectl.sha256 -> C:\Users\<USER>\.minikube\cache\linux\amd64\v1.34.0/kubectl
I1001 11:09:58.575030   17292 download.go:108] Downloading: https://dl.k8s.io/release/v1.34.0/bin/linux/amd64/kubeadm?checksum=file:https://dl.k8s.io/release/v1.34.0/bin/linux/amd64/kubeadm.sha256 -> C:\Users\<USER>\.minikube\cache\linux\amd64\v1.34.0/kubeadm
I1001 11:09:58.575030   17292 download.go:108] Downloading: https://dl.k8s.io/release/v1.34.0/bin/linux/amd64/kubelet?checksum=file:https://dl.k8s.io/release/v1.34.0/bin/linux/amd64/kubelet.sha256 -> C:\Users\<USER>\.minikube\cache\linux\amd64\v1.34.0/kubelet
I1001 11:10:16.078841   17292 ssh_runner.go:195] Run: stat -c "%s %y" /var/lib/minikube/binaries/v1.34.0/kubectl
I1001 11:10:16.085533   17292 ssh_runner.go:352] existence check for /var/lib/minikube/binaries/v1.34.0/kubectl: stat -c "%s %y" /var/lib/minikube/binaries/v1.34.0/kubectl: Process exited with status 1
stdout:

stderr:
stat: cannot statx '/var/lib/minikube/binaries/v1.34.0/kubectl': No such file or directory
I1001 11:10:16.086319   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\cache\linux\amd64\v1.34.0/kubectl --> /var/lib/minikube/binaries/v1.34.0/kubectl (60559544 bytes)
I1001 11:10:16.805384   17292 ssh_runner.go:195] Run: sudo systemctl is-active --quiet service kubelet
I1001 11:10:16.911488   17292 ssh_runner.go:195] Run: stat -c "%s %y" /var/lib/minikube/binaries/v1.34.0/kubelet
I1001 11:10:16.954128   17292 ssh_runner.go:352] existence check for /var/lib/minikube/binaries/v1.34.0/kubelet: stat -c "%s %y" /var/lib/minikube/binaries/v1.34.0/kubelet: Process exited with status 1
stdout:

stderr:
stat: cannot statx '/var/lib/minikube/binaries/v1.34.0/kubelet': No such file or directory
I1001 11:10:16.954128   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\cache\linux\amd64\v1.34.0/kubelet --> /var/lib/minikube/binaries/v1.34.0/kubelet (59195684 bytes)
I1001 11:10:17.323027   17292 ssh_runner.go:195] Run: stat -c "%s %y" /var/lib/minikube/binaries/v1.34.0/kubeadm
I1001 11:10:17.399351   17292 ssh_runner.go:352] existence check for /var/lib/minikube/binaries/v1.34.0/kubeadm: stat -c "%s %y" /var/lib/minikube/binaries/v1.34.0/kubeadm: Process exited with status 1
stdout:

stderr:
stat: cannot statx '/var/lib/minikube/binaries/v1.34.0/kubeadm': No such file or directory
I1001 11:10:17.399351   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\cache\linux\amd64\v1.34.0/kubeadm --> /var/lib/minikube/binaries/v1.34.0/kubeadm (74027192 bytes)
I1001 11:10:19.721041   17292 ssh_runner.go:195] Run: sudo mkdir -p /etc/systemd/system/kubelet.service.d /lib/systemd/system /var/tmp/minikube
I1001 11:10:19.729653   17292 ssh_runner.go:362] scp memory --> /etc/systemd/system/kubelet.service.d/10-kubeadm.conf (307 bytes)
I1001 11:10:19.750081   17292 ssh_runner.go:362] scp memory --> /lib/systemd/system/kubelet.service (352 bytes)
I1001 11:10:19.770410   17292 ssh_runner.go:362] scp memory --> /var/tmp/minikube/kubeadm.yaml.new (2209 bytes)
I1001 11:10:19.797068   17292 ssh_runner.go:195] Run: grep ************	control-plane.minikube.internal$ /etc/hosts
I1001 11:10:19.802594   17292 ssh_runner.go:195] Run: /bin/bash -c "{ grep -v $'\tcontrol-plane.minikube.internal$' "/etc/hosts"; echo "************	control-plane.minikube.internal"; } > /tmp/h.$$; sudo cp /tmp/h.$$ "/etc/hosts""
I1001 11:10:19.814889   17292 ssh_runner.go:195] Run: sudo systemctl daemon-reload
I1001 11:10:19.877122   17292 ssh_runner.go:195] Run: sudo systemctl start kubelet
I1001 11:10:20.280614   17292 certs.go:68] Setting up C:\Users\<USER>\.minikube\profiles\minikube for IP: ************
I1001 11:10:20.280614   17292 certs.go:194] generating shared ca certs ...
I1001 11:10:20.281622   17292 certs.go:226] acquiring lock for ca certs: {Name:mkd288fa537ff861aebd3b6c2ef57ce6c1e1f4f3 Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:10:20.281622   17292 certs.go:240] generating "minikubeCA" ca cert: C:\Users\<USER>\.minikube\ca.key
I1001 11:10:20.433410   17292 crypto.go:156] Writing cert to C:\Users\<USER>\.minikube\ca.crt ...
I1001 11:10:20.433410   17292 lock.go:35] WriteFile acquiring C:\Users\<USER>\.minikube\ca.crt: {Name:mk1c9eefcee276bde591c8e457006cb01dadedff Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:10:20.434615   17292 crypto.go:164] Writing key to C:\Users\<USER>\.minikube\ca.key ...
I1001 11:10:20.434615   17292 lock.go:35] WriteFile acquiring C:\Users\<USER>\.minikube\ca.key: {Name:mkb927a3992cc66130aefbe2bfd0bb8866c69853 Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:10:20.436146   17292 certs.go:240] generating "proxyClientCA" ca cert: C:\Users\<USER>\.minikube\proxy-client-ca.key
I1001 11:10:20.723844   17292 crypto.go:156] Writing cert to C:\Users\<USER>\.minikube\proxy-client-ca.crt ...
I1001 11:10:20.723844   17292 lock.go:35] WriteFile acquiring C:\Users\<USER>\.minikube\proxy-client-ca.crt: {Name:mk888d6a413a4f7c05b2c1b047fbd90a58d1fabf Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:10:20.723844   17292 crypto.go:164] Writing key to C:\Users\<USER>\.minikube\proxy-client-ca.key ...
I1001 11:10:20.723844   17292 lock.go:35] WriteFile acquiring C:\Users\<USER>\.minikube\proxy-client-ca.key: {Name:mk64b780059ed80c345cd5a5aa41137c3a9241c9 Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:10:20.724846   17292 certs.go:256] generating profile certs ...
I1001 11:10:20.725846   17292 certs.go:363] generating signed profile cert for "minikube-user": C:\Users\<USER>\.minikube\profiles\minikube\client.key
I1001 11:10:20.725846   17292 crypto.go:68] Generating cert C:\Users\<USER>\.minikube\profiles\minikube\client.crt with IP's: []
I1001 11:10:20.912374   17292 crypto.go:156] Writing cert to C:\Users\<USER>\.minikube\profiles\minikube\client.crt ...
I1001 11:10:20.912374   17292 lock.go:35] WriteFile acquiring C:\Users\<USER>\.minikube\profiles\minikube\client.crt: {Name:mkb8b941edf1d8c7c1630029a19546d3f936c7e8 Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:10:20.912374   17292 crypto.go:164] Writing key to C:\Users\<USER>\.minikube\profiles\minikube\client.key ...
I1001 11:10:20.912374   17292 lock.go:35] WriteFile acquiring C:\Users\<USER>\.minikube\profiles\minikube\client.key: {Name:mk0deaee80d0373725be3f8c9a4d7f82e4b95ee5 Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:10:20.914380   17292 certs.go:363] generating signed profile cert for "minikube": C:\Users\<USER>\.minikube\profiles\minikube\apiserver.key.7fb57e3c
I1001 11:10:20.914380   17292 crypto.go:68] Generating cert C:\Users\<USER>\.minikube\profiles\minikube\apiserver.crt.7fb57e3c with IP's: [********* 127.0.0.1 ******** ************]
I1001 11:10:21.130827   17292 crypto.go:156] Writing cert to C:\Users\<USER>\.minikube\profiles\minikube\apiserver.crt.7fb57e3c ...
I1001 11:10:21.130827   17292 lock.go:35] WriteFile acquiring C:\Users\<USER>\.minikube\profiles\minikube\apiserver.crt.7fb57e3c: {Name:mkc6a2225ebd7c74fd466ffd004f3c50088f4bfa Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:10:21.132156   17292 crypto.go:164] Writing key to C:\Users\<USER>\.minikube\profiles\minikube\apiserver.key.7fb57e3c ...
I1001 11:10:21.132156   17292 lock.go:35] WriteFile acquiring C:\Users\<USER>\.minikube\profiles\minikube\apiserver.key.7fb57e3c: {Name:mk96bd9ccf5d43a249b0128d8e62a5a987fb8ca8 Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:10:21.132697   17292 certs.go:381] copying C:\Users\<USER>\.minikube\profiles\minikube\apiserver.crt.7fb57e3c -> C:\Users\<USER>\.minikube\profiles\minikube\apiserver.crt
I1001 11:10:21.149125   17292 certs.go:385] copying C:\Users\<USER>\.minikube\profiles\minikube\apiserver.key.7fb57e3c -> C:\Users\<USER>\.minikube\profiles\minikube\apiserver.key
I1001 11:10:21.149631   17292 certs.go:363] generating signed profile cert for "aggregator": C:\Users\<USER>\.minikube\profiles\minikube\proxy-client.key
I1001 11:10:21.150165   17292 crypto.go:68] Generating cert C:\Users\<USER>\.minikube\profiles\minikube\proxy-client.crt with IP's: []
I1001 11:10:21.389454   17292 crypto.go:156] Writing cert to C:\Users\<USER>\.minikube\profiles\minikube\proxy-client.crt ...
I1001 11:10:21.389454   17292 lock.go:35] WriteFile acquiring C:\Users\<USER>\.minikube\profiles\minikube\proxy-client.crt: {Name:mk0e13ebb2f9611ff7d0a57e4ffbd770e2960ed9 Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:10:21.389454   17292 crypto.go:164] Writing key to C:\Users\<USER>\.minikube\profiles\minikube\proxy-client.key ...
I1001 11:10:21.389454   17292 lock.go:35] WriteFile acquiring C:\Users\<USER>\.minikube\profiles\minikube\proxy-client.key: {Name:mkb4fe06789ba2fd74d4b973d86af92d342e1d6d Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:10:21.410656   17292 certs.go:484] found cert: C:\Users\<USER>\.minikube\certs\ca-key.pem (1675 bytes)
I1001 11:10:21.410656   17292 certs.go:484] found cert: C:\Users\<USER>\.minikube\certs\ca.pem (1099 bytes)
I1001 11:10:21.411206   17292 certs.go:484] found cert: C:\Users\<USER>\.minikube\certs\cert.pem (1143 bytes)
I1001 11:10:21.411206   17292 certs.go:484] found cert: C:\Users\<USER>\.minikube\certs\key.pem (1675 bytes)
I1001 11:10:21.507129   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\ca.crt --> /var/lib/minikube/certs/ca.crt (1111 bytes)
I1001 11:10:21.535073   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\ca.key --> /var/lib/minikube/certs/ca.key (1675 bytes)
I1001 11:10:21.573039   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\proxy-client-ca.crt --> /var/lib/minikube/certs/proxy-client-ca.crt (1119 bytes)
I1001 11:10:21.605143   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\proxy-client-ca.key --> /var/lib/minikube/certs/proxy-client-ca.key (1679 bytes)
I1001 11:10:21.636972   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\profiles\minikube\apiserver.crt --> /var/lib/minikube/certs/apiserver.crt (1411 bytes)
I1001 11:10:21.660235   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\profiles\minikube\apiserver.key --> /var/lib/minikube/certs/apiserver.key (1675 bytes)
I1001 11:10:21.686034   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\profiles\minikube\proxy-client.crt --> /var/lib/minikube/certs/proxy-client.crt (1147 bytes)
I1001 11:10:21.709140   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\profiles\minikube\proxy-client.key --> /var/lib/minikube/certs/proxy-client.key (1679 bytes)
I1001 11:10:21.732626   17292 ssh_runner.go:362] scp C:\Users\<USER>\.minikube\ca.crt --> /usr/share/ca-certificates/minikubeCA.pem (1111 bytes)
I1001 11:10:21.759119   17292 ssh_runner.go:362] scp memory --> /var/lib/minikube/kubeconfig (738 bytes)
I1001 11:10:21.781543   17292 ssh_runner.go:195] Run: openssl version
I1001 11:10:21.789804   17292 ssh_runner.go:195] Run: sudo /bin/bash -c "test -s /usr/share/ca-certificates/minikubeCA.pem && ln -fs /usr/share/ca-certificates/minikubeCA.pem /etc/ssl/certs/minikubeCA.pem"
I1001 11:10:21.813770   17292 ssh_runner.go:195] Run: ls -la /usr/share/ca-certificates/minikubeCA.pem
I1001 11:10:21.819617   17292 certs.go:528] hashing: -rw-r--r-- 1 <USER> <GROUP> 1111 Oct  1 05:10 /usr/share/ca-certificates/minikubeCA.pem
I1001 11:10:21.823638   17292 ssh_runner.go:195] Run: openssl x509 -hash -noout -in /usr/share/ca-certificates/minikubeCA.pem
I1001 11:10:21.831517   17292 ssh_runner.go:195] Run: sudo /bin/bash -c "test -L /etc/ssl/certs/b5213941.0 || ln -fs /etc/ssl/certs/minikubeCA.pem /etc/ssl/certs/b5213941.0"
I1001 11:10:21.850719   17292 ssh_runner.go:195] Run: stat /var/lib/minikube/certs/apiserver-kubelet-client.crt
I1001 11:10:21.856059   17292 certs.go:399] 'apiserver-kubelet-client' cert doesn't exist, likely first start: stat /var/lib/minikube/certs/apiserver-kubelet-client.crt: Process exited with status 1
stdout:

stderr:
stat: cannot statx '/var/lib/minikube/certs/apiserver-kubelet-client.crt': No such file or directory
I1001 11:10:21.856575   17292 kubeadm.go:392] StartCluster: {Name:minikube KeepContext:false EmbedCerts:false MinikubeISO: KicBaseImage:gcr.io/k8s-minikube/kicbase:v0.0.48@sha256:7171c97a51623558720f8e5878e4f4637da093e2f2ed589997bedc6c1549b2b1 Memory:4000 CPUs:2 DiskSize:20000 Driver:docker HyperkitVpnKitSock: HyperkitVSockPorts:[] DockerEnv:[] ContainerVolumeMounts:[] InsecureRegistry:[] RegistryMirror:[] HostOnlyCIDR:************/24 HypervVirtualSwitch: HypervUseExternalSwitch:false HypervExternalAdapter: KVMNetwork:default KVMQemuURI:qemu:///system KVMGPU:false KVMHidden:false KVMNUMACount:1 APIServerPort:8443 DockerOpt:[] DisableDriverMounts:false NFSShare:[] NFSSharesRoot:/nfsshares UUID: NoVTXCheck:false DNSProxy:false HostDNSResolver:true HostOnlyNicType:virtio NatNicType:virtio SSHIPAddress: SSHUser:root SSHKey: SSHPort:22 KubernetesConfig:{KubernetesVersion:v1.34.0 ClusterName:minikube Namespace:default APIServerHAVIP: APIServerName:minikubeCA APIServerNames:[] APIServerIPs:[] DNSDomain:cluster.local ContainerRuntime:docker CRISocket: NetworkPlugin:cni FeatureGates: ServiceCIDR:*********/12 ImageRepository: LoadBalancerStartIP: LoadBalancerEndIP: CustomIngressCert: RegistryAliases: ExtraOptions:[] ShouldLoadCachedImages:true EnableDefaultCNI:false CNI:} Nodes:[{Name: IP:************ Port:8443 KubernetesVersion:v1.34.0 ContainerRuntime:docker ControlPlane:true Worker:true}] Addons:map[] CustomAddonImages:map[] CustomAddonRegistries:map[] VerifyComponents:map[apiserver:true system_pods:true] StartHostTimeout:6m0s ScheduledStop:<nil> ExposedPorts:[] ListenAddress: Network: Subnet: MultiNodeRequested:false ExtraDisks:0 CertExpiration:26280h0m0s MountString: Mount9PVersion:9p2000.L MountGID:docker MountIP: MountMSize:262144 MountOptions:[] MountPort:0 MountType:9p MountUID:docker BinaryMirror: DisableOptimizations:false DisableMetrics:false DisableCoreDNSLog:false CustomQemuFirmwarePath: SocketVMnetClientPath: SocketVMnetPath: StaticIP: SSHAuthSock: SSHAgentPID:0 GPUs: AutoPauseInterval:1m0s}
I1001 11:10:21.862818   17292 ssh_runner.go:195] Run: docker ps --filter status=paused --filter=name=k8s_.*_(kube-system)_ --format={{.ID}}
I1001 11:10:21.881570   17292 ssh_runner.go:195] Run: sudo ls /var/lib/kubelet/kubeadm-flags.env /var/lib/kubelet/config.yaml /var/lib/minikube/etcd
I1001 11:10:21.892506   17292 ssh_runner.go:195] Run: sudo cp /var/tmp/minikube/kubeadm.yaml.new /var/tmp/minikube/kubeadm.yaml
I1001 11:10:21.904186   17292 kubeadm.go:214] ignoring SystemVerification for kubeadm because of docker driver
I1001 11:10:21.905323   17292 ssh_runner.go:195] Run: sudo ls -la /etc/kubernetes/admin.conf /etc/kubernetes/kubelet.conf /etc/kubernetes/controller-manager.conf /etc/kubernetes/scheduler.conf
I1001 11:10:21.914825   17292 kubeadm.go:155] config check failed, skipping stale config cleanup: sudo ls -la /etc/kubernetes/admin.conf /etc/kubernetes/kubelet.conf /etc/kubernetes/controller-manager.conf /etc/kubernetes/scheduler.conf: Process exited with status 2
stdout:

stderr:
ls: cannot access '/etc/kubernetes/admin.conf': No such file or directory
ls: cannot access '/etc/kubernetes/kubelet.conf': No such file or directory
ls: cannot access '/etc/kubernetes/controller-manager.conf': No such file or directory
ls: cannot access '/etc/kubernetes/scheduler.conf': No such file or directory
I1001 11:10:21.914825   17292 kubeadm.go:157] found existing configuration files:

I1001 11:10:21.915958   17292 ssh_runner.go:195] Run: sudo grep https://control-plane.minikube.internal:8443 /etc/kubernetes/admin.conf
I1001 11:10:21.925803   17292 kubeadm.go:163] "https://control-plane.minikube.internal:8443" may not be in /etc/kubernetes/admin.conf - will remove: sudo grep https://control-plane.minikube.internal:8443 /etc/kubernetes/admin.conf: Process exited with status 2
stdout:

stderr:
grep: /etc/kubernetes/admin.conf: No such file or directory
I1001 11:10:21.926854   17292 ssh_runner.go:195] Run: sudo rm -f /etc/kubernetes/admin.conf
I1001 11:10:21.936879   17292 ssh_runner.go:195] Run: sudo grep https://control-plane.minikube.internal:8443 /etc/kubernetes/kubelet.conf
I1001 11:10:21.945736   17292 kubeadm.go:163] "https://control-plane.minikube.internal:8443" may not be in /etc/kubernetes/kubelet.conf - will remove: sudo grep https://control-plane.minikube.internal:8443 /etc/kubernetes/kubelet.conf: Process exited with status 2
stdout:

stderr:
grep: /etc/kubernetes/kubelet.conf: No such file or directory
I1001 11:10:21.946814   17292 ssh_runner.go:195] Run: sudo rm -f /etc/kubernetes/kubelet.conf
I1001 11:10:21.957285   17292 ssh_runner.go:195] Run: sudo grep https://control-plane.minikube.internal:8443 /etc/kubernetes/controller-manager.conf
I1001 11:10:21.966550   17292 kubeadm.go:163] "https://control-plane.minikube.internal:8443" may not be in /etc/kubernetes/controller-manager.conf - will remove: sudo grep https://control-plane.minikube.internal:8443 /etc/kubernetes/controller-manager.conf: Process exited with status 2
stdout:

stderr:
grep: /etc/kubernetes/controller-manager.conf: No such file or directory
I1001 11:10:21.969770   17292 ssh_runner.go:195] Run: sudo rm -f /etc/kubernetes/controller-manager.conf
I1001 11:10:21.979628   17292 ssh_runner.go:195] Run: sudo grep https://control-plane.minikube.internal:8443 /etc/kubernetes/scheduler.conf
I1001 11:10:21.989569   17292 kubeadm.go:163] "https://control-plane.minikube.internal:8443" may not be in /etc/kubernetes/scheduler.conf - will remove: sudo grep https://control-plane.minikube.internal:8443 /etc/kubernetes/scheduler.conf: Process exited with status 2
stdout:

stderr:
grep: /etc/kubernetes/scheduler.conf: No such file or directory
I1001 11:10:21.991130   17292 ssh_runner.go:195] Run: sudo rm -f /etc/kubernetes/scheduler.conf
I1001 11:10:21.999207   17292 ssh_runner.go:286] Start: /bin/bash -c "sudo env PATH="/var/lib/minikube/binaries/v1.34.0:$PATH" kubeadm init --config /var/tmp/minikube/kubeadm.yaml  --ignore-preflight-errors=DirAvailable--etc-kubernetes-manifests,DirAvailable--var-lib-minikube,DirAvailable--var-lib-minikube-etcd,FileAvailable--etc-kubernetes-manifests-kube-scheduler.yaml,FileAvailable--etc-kubernetes-manifests-kube-apiserver.yaml,FileAvailable--etc-kubernetes-manifests-kube-controller-manager.yaml,FileAvailable--etc-kubernetes-manifests-etcd.yaml,Port-10250,Swap,NumCPU,Mem,SystemVerification,FileContent--proc-sys-net-bridge-bridge-nf-call-iptables"
I1001 11:10:22.049860   17292 kubeadm.go:310] 	[WARNING Swap]: swap is supported for cgroup v2 only. The kubelet must be properly configured to use swap. Please refer to https://kubernetes.io/docs/concepts/architecture/nodes/#swap-memory, or disable swap on the node
I1001 11:10:22.108445   17292 kubeadm.go:310] 	[WARNING Service-Kubelet]: kubelet service is not enabled, please run 'systemctl enable kubelet.service'
I1001 11:10:32.231331   17292 kubeadm.go:310] [init] Using Kubernetes version: v1.34.0
I1001 11:10:32.231331   17292 kubeadm.go:310] [preflight] Running pre-flight checks
I1001 11:10:32.231331   17292 kubeadm.go:310] [preflight] Pulling images required for setting up a Kubernetes cluster
I1001 11:10:32.231855   17292 kubeadm.go:310] [preflight] This might take a minute or two, depending on the speed of your internet connection
I1001 11:10:32.231855   17292 kubeadm.go:310] [preflight] You can also perform this action beforehand using 'kubeadm config images pull'
I1001 11:10:32.231855   17292 kubeadm.go:310] [certs] Using certificateDir folder "/var/lib/minikube/certs"
I1001 11:10:32.232370   17292 out.go:252]     ▪ Generating certificates and keys ...
I1001 11:10:32.232968   17292 kubeadm.go:310] [certs] Using existing ca certificate authority
I1001 11:10:32.232968   17292 kubeadm.go:310] [certs] Using existing apiserver certificate and key on disk
I1001 11:10:32.232968   17292 kubeadm.go:310] [certs] Generating "apiserver-kubelet-client" certificate and key
I1001 11:10:32.232968   17292 kubeadm.go:310] [certs] Generating "front-proxy-ca" certificate and key
I1001 11:10:32.232968   17292 kubeadm.go:310] [certs] Generating "front-proxy-client" certificate and key
I1001 11:10:32.233504   17292 kubeadm.go:310] [certs] Generating "etcd/ca" certificate and key
I1001 11:10:32.233504   17292 kubeadm.go:310] [certs] Generating "etcd/server" certificate and key
I1001 11:10:32.233504   17292 kubeadm.go:310] [certs] etcd/server serving cert is signed for DNS names [localhost minikube] and IPs [************ 127.0.0.1 ::1]
I1001 11:10:32.233504   17292 kubeadm.go:310] [certs] Generating "etcd/peer" certificate and key
I1001 11:10:32.233504   17292 kubeadm.go:310] [certs] etcd/peer serving cert is signed for DNS names [localhost minikube] and IPs [************ 127.0.0.1 ::1]
I1001 11:10:32.233504   17292 kubeadm.go:310] [certs] Generating "etcd/healthcheck-client" certificate and key
I1001 11:10:32.234027   17292 kubeadm.go:310] [certs] Generating "apiserver-etcd-client" certificate and key
I1001 11:10:32.234027   17292 kubeadm.go:310] [certs] Generating "sa" key and public key
I1001 11:10:32.234027   17292 kubeadm.go:310] [kubeconfig] Using kubeconfig folder "/etc/kubernetes"
I1001 11:10:32.234027   17292 kubeadm.go:310] [kubeconfig] Writing "admin.conf" kubeconfig file
I1001 11:10:32.234027   17292 kubeadm.go:310] [kubeconfig] Writing "super-admin.conf" kubeconfig file
I1001 11:10:32.234027   17292 kubeadm.go:310] [kubeconfig] Writing "kubelet.conf" kubeconfig file
I1001 11:10:32.234027   17292 kubeadm.go:310] [kubeconfig] Writing "controller-manager.conf" kubeconfig file
I1001 11:10:32.234027   17292 kubeadm.go:310] [kubeconfig] Writing "scheduler.conf" kubeconfig file
I1001 11:10:32.234553   17292 kubeadm.go:310] [etcd] Creating static Pod manifest for local etcd in "/etc/kubernetes/manifests"
I1001 11:10:32.234553   17292 kubeadm.go:310] [control-plane] Using manifest folder "/etc/kubernetes/manifests"
I1001 11:10:32.235082   17292 out.go:252]     ▪ Booting up control plane ...
I1001 11:10:32.235082   17292 kubeadm.go:310] [control-plane] Creating static Pod manifest for "kube-apiserver"
I1001 11:10:32.235603   17292 kubeadm.go:310] [control-plane] Creating static Pod manifest for "kube-controller-manager"
I1001 11:10:32.235603   17292 kubeadm.go:310] [control-plane] Creating static Pod manifest for "kube-scheduler"
I1001 11:10:32.235603   17292 kubeadm.go:310] [kubelet-start] Writing kubelet environment file with flags to file "/var/lib/kubelet/kubeadm-flags.env"
I1001 11:10:32.235603   17292 kubeadm.go:310] [kubelet-start] Writing kubelet configuration to file "/var/lib/kubelet/instance-config.yaml"
I1001 11:10:32.236126   17292 kubeadm.go:310] [patches] Applied patch of type "application/strategic-merge-patch+json" to target "kubeletconfiguration"
I1001 11:10:32.236126   17292 kubeadm.go:310] [kubelet-start] Writing kubelet configuration to file "/var/lib/kubelet/config.yaml"
I1001 11:10:32.236126   17292 kubeadm.go:310] [kubelet-start] Starting the kubelet
I1001 11:10:32.236126   17292 kubeadm.go:310] [wait-control-plane] Waiting for the kubelet to boot up the control plane as static Pods from directory "/etc/kubernetes/manifests"
I1001 11:10:32.236126   17292 kubeadm.go:310] [kubelet-check] Waiting for a healthy kubelet at http://127.0.0.1:10248/healthz. This can take up to 4m0s
I1001 11:10:32.236640   17292 kubeadm.go:310] [kubelet-check] The kubelet is healthy after 501.19457ms
I1001 11:10:32.236640   17292 kubeadm.go:310] [control-plane-check] Waiting for healthy control plane components. This can take up to 4m0s
I1001 11:10:32.236640   17292 kubeadm.go:310] [control-plane-check] Checking kube-apiserver at https://************:8443/livez
I1001 11:10:32.236640   17292 kubeadm.go:310] [control-plane-check] Checking kube-controller-manager at https://127.0.0.1:10257/healthz
I1001 11:10:32.237160   17292 kubeadm.go:310] [control-plane-check] Checking kube-scheduler at https://127.0.0.1:10259/livez
I1001 11:10:32.237160   17292 kubeadm.go:310] [control-plane-check] kube-controller-manager is healthy after 3.285838772s
I1001 11:10:32.237160   17292 kubeadm.go:310] [control-plane-check] kube-scheduler is healthy after 4.169442881s
I1001 11:10:32.237160   17292 kubeadm.go:310] [control-plane-check] kube-apiserver is healthy after 6.001705951s
I1001 11:10:32.237160   17292 kubeadm.go:310] [upload-config] Storing the configuration used in ConfigMap "kubeadm-config" in the "kube-system" Namespace
I1001 11:10:32.237679   17292 kubeadm.go:310] [kubelet] Creating a ConfigMap "kubelet-config" in namespace kube-system with the configuration for the kubelets in the cluster
I1001 11:10:32.237679   17292 kubeadm.go:310] [upload-certs] Skipping phase. Please see --upload-certs
I1001 11:10:32.237679   17292 kubeadm.go:310] [mark-control-plane] Marking the node minikube as control-plane by adding the labels: [node-role.kubernetes.io/control-plane node.kubernetes.io/exclude-from-external-load-balancers]
I1001 11:10:32.238246   17292 kubeadm.go:310] [bootstrap-token] Using token: bis3an.xd5uec6860qsru9j
I1001 11:10:32.238246   17292 out.go:252]     ▪ Configuring RBAC rules ...
I1001 11:10:32.238246   17292 kubeadm.go:310] [bootstrap-token] Configuring bootstrap tokens, cluster-info ConfigMap, RBAC Roles
I1001 11:10:32.238763   17292 kubeadm.go:310] [bootstrap-token] Configured RBAC rules to allow Node Bootstrap tokens to get nodes
I1001 11:10:32.238763   17292 kubeadm.go:310] [bootstrap-token] Configured RBAC rules to allow Node Bootstrap tokens to post CSRs in order for nodes to get long term certificate credentials
I1001 11:10:32.238763   17292 kubeadm.go:310] [bootstrap-token] Configured RBAC rules to allow the csrapprover controller automatically approve CSRs from a Node Bootstrap Token
I1001 11:10:32.239448   17292 kubeadm.go:310] [bootstrap-token] Configured RBAC rules to allow certificate rotation for all node client certificates in the cluster
I1001 11:10:32.239448   17292 kubeadm.go:310] [bootstrap-token] Creating the "cluster-info" ConfigMap in the "kube-public" namespace
I1001 11:10:32.239448   17292 kubeadm.go:310] [kubelet-finalize] Updating "/etc/kubernetes/kubelet.conf" to point to a rotatable kubelet client certificate and key
I1001 11:10:32.239448   17292 kubeadm.go:310] [addons] Applied essential addon: CoreDNS
I1001 11:10:32.239448   17292 kubeadm.go:310] [addons] Applied essential addon: kube-proxy
I1001 11:10:32.239448   17292 kubeadm.go:310] 
I1001 11:10:32.240095   17292 kubeadm.go:310] Your Kubernetes control-plane has initialized successfully!
I1001 11:10:32.240095   17292 kubeadm.go:310] 
I1001 11:10:32.240095   17292 kubeadm.go:310] To start using your cluster, you need to run the following as a regular user:
I1001 11:10:32.240095   17292 kubeadm.go:310] 
I1001 11:10:32.240095   17292 kubeadm.go:310]   mkdir -p $HOME/.kube
I1001 11:10:32.240095   17292 kubeadm.go:310]   sudo cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
I1001 11:10:32.240095   17292 kubeadm.go:310]   sudo chown $(id -u):$(id -g) $HOME/.kube/config
I1001 11:10:32.240095   17292 kubeadm.go:310] 
I1001 11:10:32.240095   17292 kubeadm.go:310] Alternatively, if you are the root user, you can run:
I1001 11:10:32.240095   17292 kubeadm.go:310] 
I1001 11:10:32.240095   17292 kubeadm.go:310]   export KUBECONFIG=/etc/kubernetes/admin.conf
I1001 11:10:32.240095   17292 kubeadm.go:310] 
I1001 11:10:32.240634   17292 kubeadm.go:310] You should now deploy a pod network to the cluster.
I1001 11:10:32.240634   17292 kubeadm.go:310] Run "kubectl apply -f [podnetwork].yaml" with one of the options listed at:
I1001 11:10:32.240634   17292 kubeadm.go:310]   https://kubernetes.io/docs/concepts/cluster-administration/addons/
I1001 11:10:32.240634   17292 kubeadm.go:310] 
I1001 11:10:32.241159   17292 kubeadm.go:310] You can now join any number of control-plane nodes by copying certificate authorities
I1001 11:10:32.241159   17292 kubeadm.go:310] and service account keys on each node and then running the following as root:
I1001 11:10:32.241159   17292 kubeadm.go:310] 
I1001 11:10:32.241159   17292 kubeadm.go:310]   kubeadm join control-plane.minikube.internal:8443 --token bis3an.xd5uec6860qsru9j \
I1001 11:10:32.241159   17292 kubeadm.go:310] 	--discovery-token-ca-cert-hash sha256:4a280bab8d229e68a411952dc7d6e92c6c867751c6d5daff2430cf8629785a26 \
I1001 11:10:32.241159   17292 kubeadm.go:310] 	--control-plane 
I1001 11:10:32.241159   17292 kubeadm.go:310] 
I1001 11:10:32.241690   17292 kubeadm.go:310] Then you can join any number of worker nodes by running the following on each as root:
I1001 11:10:32.241690   17292 kubeadm.go:310] 
I1001 11:10:32.241690   17292 kubeadm.go:310] kubeadm join control-plane.minikube.internal:8443 --token bis3an.xd5uec6860qsru9j \
I1001 11:10:32.241690   17292 kubeadm.go:310] 	--discovery-token-ca-cert-hash sha256:4a280bab8d229e68a411952dc7d6e92c6c867751c6d5daff2430cf8629785a26 
I1001 11:10:32.241690   17292 cni.go:84] Creating CNI manager for ""
I1001 11:10:32.241690   17292 cni.go:158] "docker" driver + "docker" container runtime found on kubernetes v1.24+, recommending bridge
I1001 11:10:32.242784   17292 out.go:179] 🔗  Configuring bridge CNI (Container Networking Interface) ...
I1001 11:10:32.244695   17292 ssh_runner.go:195] Run: sudo mkdir -p /etc/cni/net.d
I1001 11:10:32.254751   17292 ssh_runner.go:362] scp memory --> /etc/cni/net.d/1-k8s.conflist (496 bytes)
I1001 11:10:32.288795   17292 ssh_runner.go:195] Run: /bin/bash -c "cat /proc/$(pgrep kube-apiserver)/oom_adj"
I1001 11:10:32.293829   17292 ssh_runner.go:195] Run: sudo /var/lib/minikube/binaries/v1.34.0/kubectl --kubeconfig=/var/lib/minikube/kubeconfig label --overwrite nodes minikube minikube.k8s.io/updated_at=2025_10_01T11_10_32_0700 minikube.k8s.io/version=v1.37.0 minikube.k8s.io/commit=65318f4cfff9c12cc87ec9eb8f4cdd57b25047f3 minikube.k8s.io/name=minikube minikube.k8s.io/primary=true
I1001 11:10:32.294873   17292 ssh_runner.go:195] Run: sudo /var/lib/minikube/binaries/v1.34.0/kubectl create clusterrolebinding minikube-rbac --clusterrole=cluster-admin --serviceaccount=kube-system:default --kubeconfig=/var/lib/minikube/kubeconfig
I1001 11:10:32.302826   17292 ops.go:34] apiserver oom_adj: -16
I1001 11:10:32.419250   17292 kubeadm.go:1105] duration metric: took 126.0447ms to wait for elevateKubeSystemPrivileges
I1001 11:10:32.419250   17292 kubeadm.go:394] duration metric: took 10.5626756s to StartCluster
I1001 11:10:32.419250   17292 settings.go:142] acquiring lock: {Name:mkf6aaaf55fe4149739eb13d027b1ecf8bb768bf Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:10:32.419250   17292 settings.go:150] Updating kubeconfig:  C:\Users\<USER>\.kube\config
I1001 11:10:32.420297   17292 lock.go:35] WriteFile acquiring C:\Users\<USER>\.kube\config: {Name:mkf0e8262f3b5ccbd05638caaac3c226b5e47aa6 Clock:{} Delay:500ms Timeout:1m0s Cancel:<nil>}
I1001 11:10:32.421920   17292 config.go:182] Loaded profile config "minikube": Driver=docker, ContainerRuntime=docker, KubernetesVersion=v1.34.0
I1001 11:10:32.421920   17292 ssh_runner.go:195] Run: /bin/bash -c "sudo /var/lib/minikube/binaries/v1.34.0/kubectl --kubeconfig=/var/lib/minikube/kubeconfig -n kube-system get configmap coredns -o yaml"
I1001 11:10:32.422999   17292 start.go:235] Will wait 6m0s for node &{Name: IP:************ Port:8443 KubernetesVersion:v1.34.0 ContainerRuntime:docker ControlPlane:true Worker:true}
I1001 11:10:32.422999   17292 out.go:179] 🔎  Verifying Kubernetes components...
I1001 11:10:32.427399   17292 ssh_runner.go:195] Run: sudo systemctl daemon-reload
I1001 11:10:32.424574   17292 addons.go:511] enable addons start: toEnable=map[ambassador:false amd-gpu-device-plugin:false auto-pause:false cloud-spanner:false csi-hostpath-driver:false dashboard:false default-storageclass:true efk:false freshpod:false gcp-auth:false gvisor:false headlamp:false inaccel:false ingress:false ingress-dns:false inspektor-gadget:false istio:false istio-provisioner:false kong:false kubeflow:false kubetail:false kubevirt:false logviewer:false metallb:false metrics-server:false nvidia-device-plugin:false nvidia-driver-installer:false nvidia-gpu-device-plugin:false olm:false pod-security-policy:false portainer:false registry:false registry-aliases:false registry-creds:false storage-provisioner:true storage-provisioner-gluster:false storage-provisioner-rancher:false volcano:false volumesnapshots:false yakd:false]
I1001 11:10:32.427399   17292 addons.go:69] Setting storage-provisioner=true in profile "minikube"
I1001 11:10:32.427399   17292 addons.go:69] Setting default-storageclass=true in profile "minikube"
I1001 11:10:32.427399   17292 addons_storage_classes.go:33] enableOrDisableStorageClasses default-storageclass=true on "minikube"
I1001 11:10:32.452133   17292 cli_runner.go:164] Run: docker container inspect minikube --format={{.State.Status}}
I1001 11:10:32.458779   17292 addons.go:238] Setting addon storage-provisioner=true in "minikube"
I1001 11:10:32.459847   17292 host.go:66] Checking if "minikube" exists ...
I1001 11:10:32.485374   17292 cli_runner.go:164] Run: docker container inspect minikube --format={{.State.Status}}
I1001 11:10:32.554341   17292 out.go:179]     ▪ Using image gcr.io/k8s-minikube/storage-provisioner:v5
I1001 11:10:32.554861   17292 addons.go:435] installing /etc/kubernetes/addons/storage-provisioner.yaml
I1001 11:10:32.554861   17292 ssh_runner.go:362] scp memory --> /etc/kubernetes/addons/storage-provisioner.yaml (2676 bytes)
I1001 11:10:32.562319   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "22/tcp") 0).HostPort}}'" minikube
I1001 11:10:32.597171   17292 ssh_runner.go:195] Run: /bin/bash -c "sudo /var/lib/minikube/binaries/v1.34.0/kubectl --kubeconfig=/var/lib/minikube/kubeconfig -n kube-system get configmap coredns -o yaml | sed -e '/^        forward . \/etc\/resolv.conf.*/i \        hosts {\n           ************** host.minikube.internal\n           fallthrough\n        }' -e '/^        errors *$/i \        log' | sudo /var/lib/minikube/binaries/v1.34.0/kubectl --kubeconfig=/var/lib/minikube/kubeconfig replace -f -"
I1001 11:10:32.633814   17292 sshutil.go:53] new ssh client: &{IP:127.0.0.1 Port:54350 SSHKeyPath:C:\Users\<USER>\.minikube\machines\minikube\id_rsa Username:docker}
I1001 11:10:32.638477   17292 ssh_runner.go:195] Run: sudo systemctl start kubelet
I1001 11:10:32.731046   17292 addons.go:238] Setting addon default-storageclass=true in "minikube"
I1001 11:10:32.731046   17292 host.go:66] Checking if "minikube" exists ...
I1001 11:10:32.745107   17292 cli_runner.go:164] Run: docker container inspect minikube --format={{.State.Status}}
I1001 11:10:32.783996   17292 addons.go:435] installing /etc/kubernetes/addons/storageclass.yaml
I1001 11:10:32.783996   17292 ssh_runner.go:362] scp storageclass/storageclass.yaml --> /etc/kubernetes/addons/storageclass.yaml (271 bytes)
I1001 11:10:32.797634   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "22/tcp") 0).HostPort}}'" minikube
I1001 11:10:32.859591   17292 sshutil.go:53] new ssh client: &{IP:127.0.0.1 Port:54350 SSHKeyPath:C:\Users\<USER>\.minikube\machines\minikube\id_rsa Username:docker}
I1001 11:10:32.916601   17292 ssh_runner.go:195] Run: sudo KUBECONFIG=/var/lib/minikube/kubeconfig /var/lib/minikube/binaries/v1.34.0/kubectl apply -f /etc/kubernetes/addons/storage-provisioner.yaml
I1001 11:10:33.026578   17292 start.go:976] {"host.minikube.internal": **************} host record injected into CoreDNS's ConfigMap
I1001 11:10:33.034558   17292 cli_runner.go:164] Run: docker container inspect -f "'{{(index (index .NetworkSettings.Ports "8443/tcp") 0).HostPort}}'" minikube
I1001 11:10:33.060447   17292 ssh_runner.go:195] Run: sudo KUBECONFIG=/var/lib/minikube/kubeconfig /var/lib/minikube/binaries/v1.34.0/kubectl apply -f /etc/kubernetes/addons/storageclass.yaml
I1001 11:10:33.096704   17292 api_server.go:52] waiting for apiserver process to appear ...
I1001 11:10:33.098287   17292 ssh_runner.go:195] Run: sudo pgrep -xnf kube-apiserver.*minikube.*
I1001 11:10:33.327144   17292 api_server.go:72] duration metric: took 904.1454ms to wait for apiserver process to appear ...
I1001 11:10:33.327144   17292 api_server.go:88] waiting for apiserver healthz status ...
I1001 11:10:33.328193   17292 api_server.go:253] Checking apiserver healthz at https://127.0.0.1:54348/healthz ...
I1001 11:10:33.336428   17292 api_server.go:279] https://127.0.0.1:54348/healthz returned 200:
ok
I1001 11:10:33.338536   17292 api_server.go:141] control plane version: v1.34.0
I1001 11:10:33.338536   17292 api_server.go:131] duration metric: took 11.3915ms to wait for apiserver health ...
I1001 11:10:33.338536   17292 system_pods.go:43] waiting for kube-system pods to appear ...
I1001 11:10:33.363993   17292 out.go:179] 🌟  Enabled addons: storage-provisioner, default-storageclass
I1001 11:10:33.363993   17292 addons.go:514] duration metric: took 941.5394ms for enable addons: enabled=[storage-provisioner default-storageclass]
I1001 11:10:33.430255   17292 system_pods.go:59] 5 kube-system pods found
I1001 11:10:33.430255   17292 system_pods.go:61] "etcd-minikube" [97ca4af3-4298-481a-91d9-2d28b22cbbdf] Running / Ready:ContainersNotReady (containers with unready status: [etcd]) / ContainersReady:ContainersNotReady (containers with unready status: [etcd])
I1001 11:10:33.430255   17292 system_pods.go:61] "kube-apiserver-minikube" [29f9e9fc-bc78-4cb4-b091-b44f09c947d3] Running / Ready:ContainersNotReady (containers with unready status: [kube-apiserver]) / ContainersReady:ContainersNotReady (containers with unready status: [kube-apiserver])
I1001 11:10:33.430255   17292 system_pods.go:61] "kube-controller-manager-minikube" [25cafb81-c774-4e96-a353-497a45ff9f77] Running / Ready:ContainersNotReady (containers with unready status: [kube-controller-manager]) / ContainersReady:ContainersNotReady (containers with unready status: [kube-controller-manager])
I1001 11:10:33.430255   17292 system_pods.go:61] "kube-scheduler-minikube" [56687ed9-9dab-43ed-befc-c132db4af86c] Running / Ready:ContainersNotReady (containers with unready status: [kube-scheduler]) / ContainersReady:ContainersNotReady (containers with unready status: [kube-scheduler])
I1001 11:10:33.430255   17292 system_pods.go:61] "storage-provisioner" [ddca05ed-1482-405d-9281-1b3ac06c6e73] Pending: PodScheduled:Unschedulable (0/1 nodes are available: 1 node(s) had untolerated taint {node.kubernetes.io/not-ready: }. no new claims to deallocate, preemption: 0/1 nodes are available: 1 Preemption is not helpful for scheduling.)
I1001 11:10:33.430255   17292 system_pods.go:74] duration metric: took 91.7196ms to wait for pod list to return data ...
I1001 11:10:33.430255   17292 kubeadm.go:578] duration metric: took 1.0072565s to wait for: map[apiserver:true system_pods:true]
I1001 11:10:33.430255   17292 node_conditions.go:102] verifying NodePressure condition ...
I1001 11:10:33.438604   17292 node_conditions.go:122] node storage ephemeral capacity is 1055762868Ki
I1001 11:10:33.438604   17292 node_conditions.go:123] node cpu capacity is 4
I1001 11:10:33.439819   17292 node_conditions.go:105] duration metric: took 9.5634ms to run NodePressure ...
I1001 11:10:33.439819   17292 start.go:241] waiting for startup goroutines ...
I1001 11:10:33.540820   17292 kapi.go:214] "coredns" deployment in "kube-system" namespace and "minikube" context rescaled to 1 replicas
I1001 11:10:33.540820   17292 start.go:246] waiting for cluster config update ...
I1001 11:10:33.540820   17292 start.go:255] writing updated cluster config ...
I1001 11:10:33.554465   17292 ssh_runner.go:195] Run: rm -f paused
I1001 11:10:33.742981   17292 start.go:617] kubectl: 1.32.2, cluster: 1.34.0 (minor skew: 2)
I1001 11:10:33.744779   17292 out.go:203] 
W1001 11:10:33.747954   17292 out.go:285] ❗  C:\Program Files\Docker\Docker\resources\bin\kubectl.exe is version 1.32.2, which may have incompatibilities with Kubernetes 1.34.0.
I1001 11:10:33.749115   17292 out.go:179]     ▪ Want kubectl v1.34.0? Try 'minikube kubectl -- get pods -A'
I1001 11:10:33.758079   17292 out.go:179] 🏄  Done! kubectl is now configured to use "minikube" cluster and "default" namespace by default


==> Docker <==
Oct 01 05:09:43 minikube dockerd[1543]: time="2025-10-01T05:09:43.752082374Z" level=info msg="CDI directory does not exist, skipping: failed to monitor for changes: no such file or directory" dir=/etc/cdi
Oct 01 05:09:43 minikube dockerd[1543]: time="2025-10-01T05:09:43.763963596Z" level=info msg="Creating a containerd client" address=/run/containerd/containerd.sock timeout=1m0s
Oct 01 05:09:43 minikube dockerd[1543]: time="2025-10-01T05:09:43.771527230Z" level=info msg="[graphdriver] trying configured driver: overlay2"
Oct 01 05:09:43 minikube dockerd[1543]: time="2025-10-01T05:09:43.781896247Z" level=info msg="Loading containers: start."
Oct 01 05:09:44 minikube dockerd[1543]: time="2025-10-01T05:09:44.954659160Z" level=warning msg="Error (Unable to complete atomic operation, key modified) deleting object [endpoint_count 5c13e0b5ffd8da9a711fee4fdf1ef187f0fd3dffcea35306b4a715829b235e82], retrying...."
Oct 01 05:09:45 minikube dockerd[1543]: time="2025-10-01T05:09:45.010385377Z" level=info msg="Loading containers: done."
Oct 01 05:09:45 minikube dockerd[1543]: time="2025-10-01T05:09:45.020028462Z" level=info msg="Docker daemon" commit=249d679 containerd-snapshotter=false storage-driver=overlay2 version=28.4.0
Oct 01 05:09:45 minikube dockerd[1543]: time="2025-10-01T05:09:45.020112131Z" level=info msg="Initializing buildkit"
Oct 01 05:09:45 minikube dockerd[1543]: time="2025-10-01T05:09:45.042047379Z" level=info msg="Completed buildkit initialization"
Oct 01 05:09:45 minikube dockerd[1543]: time="2025-10-01T05:09:45.047726098Z" level=info msg="Daemon has completed initialization"
Oct 01 05:09:45 minikube dockerd[1543]: time="2025-10-01T05:09:45.047805090Z" level=info msg="API listen on /run/docker.sock"
Oct 01 05:09:45 minikube dockerd[1543]: time="2025-10-01T05:09:45.047823612Z" level=info msg="API listen on /var/run/docker.sock"
Oct 01 05:09:45 minikube dockerd[1543]: time="2025-10-01T05:09:45.047837516Z" level=info msg="API listen on [::]:2376"
Oct 01 05:09:45 minikube systemd[1]: Started Docker Application Container Engine.
Oct 01 05:10:25 minikube cri-dockerd[1392]: time="2025-10-01T05:10:25Z" level=info msg="Will attempt to re-write config file /var/lib/docker/containers/9bfa43a0f3a99b484b9c9a78cf430078c457eab0ec4d4d12d80f9cc742f39539/resolv.conf as [nameserver ************** options ndots:0]"
Oct 01 05:10:25 minikube cri-dockerd[1392]: time="2025-10-01T05:10:25Z" level=info msg="Will attempt to re-write config file /var/lib/docker/containers/c9255e4a610bd5643a67247724044c47e9cd71ba130a0a0157708814984fbcf2/resolv.conf as [nameserver ************** options ndots:0]"
Oct 01 05:10:25 minikube cri-dockerd[1392]: time="2025-10-01T05:10:25Z" level=info msg="Will attempt to re-write config file /var/lib/docker/containers/8d1ca89f684c6113e40966213cb5ec6ba1ac0d0ad00d30ff75fc06dde2c49440/resolv.conf as [nameserver ************** options ndots:0]"
Oct 01 05:10:25 minikube cri-dockerd[1392]: time="2025-10-01T05:10:25Z" level=info msg="Will attempt to re-write config file /var/lib/docker/containers/0288c4277ae89a1efc60561ce35026900a599f848297a37cc13cbdec0600d355/resolv.conf as [nameserver ************** options ndots:0]"
Oct 01 05:10:37 minikube cri-dockerd[1392]: time="2025-10-01T05:10:37Z" level=info msg="Will attempt to re-write config file /var/lib/docker/containers/75f11ff42de041394c014d162573d9b387311e07483c6402d16e63fcc85f12c2/resolv.conf as [nameserver ************** options ndots:0]"
Oct 01 05:10:37 minikube cri-dockerd[1392]: time="2025-10-01T05:10:37Z" level=info msg="Will attempt to re-write config file /var/lib/docker/containers/00da22a329b45b7d3daee2b59737f2b3b11ea2c6ee85dba57193bd23561476f0/resolv.conf as [nameserver ************** options ndots:0]"
Oct 01 05:10:38 minikube cri-dockerd[1392]: time="2025-10-01T05:10:38Z" level=info msg="Will attempt to re-write config file /var/lib/docker/containers/69fa6a8d47f903fe19a60cefd3048f73ea4ca6a597108066a04d41d1a52c6f65/resolv.conf as [nameserver ************** options ndots:0]"
Oct 01 05:10:41 minikube cri-dockerd[1392]: time="2025-10-01T05:10:41Z" level=info msg="Docker cri received runtime config &RuntimeConfig{NetworkConfig:&NetworkConfig{PodCidr:**********/24,},}"
Oct 01 05:10:58 minikube dockerd[1543]: time="2025-10-01T05:10:58.515892523Z" level=info msg="ignoring event" container=9a8ce4122766ecf8b27edc7852c26f8bfbd1d36568ce08f00996341d0b29c9ad module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:17:54 minikube cri-dockerd[1392]: time="2025-10-01T05:17:54Z" level=info msg="Will attempt to re-write config file /var/lib/docker/containers/9bc7e70dc97b057c4a1699b48cc02097b92680531c80010f031167cf2a788c66/resolv.conf as [nameserver *********0 search default.svc.cluster.local svc.cluster.local cluster.local options ndots:5]"
Oct 01 05:17:54 minikube cri-dockerd[1392]: time="2025-10-01T05:17:54Z" level=info msg="Will attempt to re-write config file /var/lib/docker/containers/68310063c337a732ad31bb6a50b80440abe3711042b75b652d2f46fa8dd3ec95/resolv.conf as [nameserver *********0 search default.svc.cluster.local svc.cluster.local cluster.local options ndots:5]"
Oct 01 05:18:08 minikube cri-dockerd[1392]: time="2025-10-01T05:18:08Z" level=info msg="Pulling image jsmasterypro/kubernetes-demo-api:latest: e4ddc696e653: Extracting [===================>                               ]  3.637MB/9.299MB"
Oct 01 05:18:11 minikube cri-dockerd[1392]: time="2025-10-01T05:18:11Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Downloaded newer image for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:18:14 minikube cri-dockerd[1392]: time="2025-10-01T05:18:14Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Image is up to date for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:18:14 minikube dockerd[1543]: time="2025-10-01T05:18:14.466973856Z" level=info msg="ignoring event" container=1feb5b509dead7b93ca1999e51aac190715ea42619e22e7ea04111b5459f961c module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:18:17 minikube dockerd[1543]: time="2025-10-01T05:18:17.017602552Z" level=info msg="ignoring event" container=e0e01ebd147d3cccd51b7314449ae51426562b3ba8e1355e3ea6982c6a30dde2 module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:18:17 minikube cri-dockerd[1392]: time="2025-10-01T05:18:17Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Image is up to date for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:18:21 minikube cri-dockerd[1392]: time="2025-10-01T05:18:21Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Image is up to date for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:18:21 minikube dockerd[1543]: time="2025-10-01T05:18:21.572735610Z" level=info msg="ignoring event" container=99bb9bc0446bda80b1419b0438155d407d7667f8de7c0a78f1e6839f141e85ca module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:18:26 minikube dockerd[1543]: time="2025-10-01T05:18:26.061751647Z" level=info msg="ignoring event" container=7e7d4215dba7dd1bffc8521cba5deb1f8e4f8e6ec9615d92d0c36dc16840295d module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:18:37 minikube cri-dockerd[1392]: time="2025-10-01T05:18:37Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Image is up to date for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:18:40 minikube dockerd[1543]: time="2025-10-01T05:18:40.969665535Z" level=info msg="ignoring event" container=ea9b9a705ac4183e171adcc20a20ed34c79bde45a004dfdb617b90d0ed964734 module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:18:52 minikube cri-dockerd[1392]: time="2025-10-01T05:18:52Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Image is up to date for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:18:56 minikube dockerd[1543]: time="2025-10-01T05:18:56.169464103Z" level=info msg="ignoring event" container=03f61f1eff269e1c0ffd6541c5f82d2f183ffdaf2cee195b2c6d440080cf2d18 module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:19:09 minikube cri-dockerd[1392]: time="2025-10-01T05:19:09Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Image is up to date for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:19:15 minikube dockerd[1543]: time="2025-10-01T05:19:15.608750113Z" level=info msg="ignoring event" container=3d95ec75de9089131c250b286a4d6433ad59936130b78e7d3d3ad20deebe6441 module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:19:28 minikube cri-dockerd[1392]: time="2025-10-01T05:19:28Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Image is up to date for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:19:33 minikube dockerd[1543]: time="2025-10-01T05:19:33.000404329Z" level=info msg="ignoring event" container=ed52cbba5772b25b7935378c010203a08b9312253c9c91eabad450bec3328557 module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:20:00 minikube cri-dockerd[1392]: time="2025-10-01T05:20:00Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Image is up to date for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:20:04 minikube dockerd[1543]: time="2025-10-01T05:20:04.881495805Z" level=info msg="ignoring event" container=9c460b4793d48f68bade6da539841e807a5f4fa7c0afc0255a9c388ba3cd378f module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:20:13 minikube cri-dockerd[1392]: time="2025-10-01T05:20:13Z" level=error msg="error getting RW layer size for container ID '3d95ec75de9089131c250b286a4d6433ad59936130b78e7d3d3ad20deebe6441': Error response from daemon: No such container: 3d95ec75de9089131c250b286a4d6433ad59936130b78e7d3d3ad20deebe6441"
Oct 01 05:20:13 minikube cri-dockerd[1392]: time="2025-10-01T05:20:13Z" level=error msg="Set backoffDuration to : 1m0s for container ID '3d95ec75de9089131c250b286a4d6433ad59936130b78e7d3d3ad20deebe6441'"
Oct 01 05:20:19 minikube cri-dockerd[1392]: time="2025-10-01T05:20:19Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Image is up to date for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:20:24 minikube dockerd[1543]: time="2025-10-01T05:20:24.362377967Z" level=info msg="ignoring event" container=59ec17cc034f2f9750883003af29951b691ff394d54b452f81b634a304a9676a module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:20:33 minikube cri-dockerd[1392]: time="2025-10-01T05:20:33Z" level=error msg="error getting RW layer size for container ID 'ed52cbba5772b25b7935378c010203a08b9312253c9c91eabad450bec3328557': Error response from daemon: No such container: ed52cbba5772b25b7935378c010203a08b9312253c9c91eabad450bec3328557"
Oct 01 05:20:33 minikube cri-dockerd[1392]: time="2025-10-01T05:20:33Z" level=error msg="Set backoffDuration to : 1m0s for container ID 'ed52cbba5772b25b7935378c010203a08b9312253c9c91eabad450bec3328557'"
Oct 01 05:21:38 minikube cri-dockerd[1392]: time="2025-10-01T05:21:38Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Image is up to date for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:21:43 minikube dockerd[1543]: time="2025-10-01T05:21:43.606282900Z" level=info msg="ignoring event" container=a6c84843cff24c904ce6ba4d1fef6a35892c340df370807e2b7d5062b10b559d module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:21:53 minikube cri-dockerd[1392]: time="2025-10-01T05:21:53Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Image is up to date for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:21:58 minikube dockerd[1543]: time="2025-10-01T05:21:58.450767841Z" level=info msg="ignoring event" container=eeb2bcd0ae4f280b9e3a0dd834cb029c5ef930087548273a69d63789f1388d3e module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:24:33 minikube cri-dockerd[1392]: time="2025-10-01T05:24:33Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Image is up to date for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:24:37 minikube dockerd[1543]: time="2025-10-01T05:24:37.554631326Z" level=info msg="ignoring event" container=c1dcc613411e77cae989b96f9f3927c64965902f2f133d32fe84917060eb676a module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"
Oct 01 05:24:43 minikube cri-dockerd[1392]: time="2025-10-01T05:24:43Z" level=error msg="error getting RW layer size for container ID 'a6c84843cff24c904ce6ba4d1fef6a35892c340df370807e2b7d5062b10b559d': Error response from daemon: No such container: a6c84843cff24c904ce6ba4d1fef6a35892c340df370807e2b7d5062b10b559d"
Oct 01 05:24:43 minikube cri-dockerd[1392]: time="2025-10-01T05:24:43Z" level=error msg="Set backoffDuration to : 1m0s for container ID 'a6c84843cff24c904ce6ba4d1fef6a35892c340df370807e2b7d5062b10b559d'"
Oct 01 05:24:51 minikube cri-dockerd[1392]: time="2025-10-01T05:24:51Z" level=info msg="Stop pulling image jsmasterypro/kubernetes-demo-api:latest: Status: Image is up to date for jsmasterypro/kubernetes-demo-api:latest"
Oct 01 05:24:56 minikube dockerd[1543]: time="2025-10-01T05:24:56.244999684Z" level=info msg="ignoring event" container=d8015551a05c14a96b800b4bf6abee39a8f24957e248d73e83841cf131cbde06 module=libcontainerd namespace=moby topic=/tasks/delete type="*events.TaskDelete"


==> container status <==
CONTAINER           IMAGE                                                                                                      CREATED              STATE               NAME                      ATTEMPT             POD ID              POD
d8015551a05c1       jsmasterypro/kubernetes-demo-api@sha256:369d05ccf262d2b4612df2272bef0374ca0d17bb75c45544bc07500552871627   About a minute ago   Exited              kubernetes-demo-api       6                   68310063c337a       kubernetes-demo-api-54db6ffd65-cp9sx
c1dcc613411e7       jsmasterypro/kubernetes-demo-api@sha256:369d05ccf262d2b4612df2272bef0374ca0d17bb75c45544bc07500552871627   2 minutes ago        Exited              kubernetes-demo-api       6                   9bc7e70dc97b0       kubernetes-demo-api-54db6ffd65-cjqpf
e8a3ae3e70ac2       6e38f40d628db                                                                                              15 minutes ago       Running             storage-provisioner       1                   00da22a329b45       storage-provisioner
ef62f38038a7b       52546a367cc9e                                                                                              16 minutes ago       Running             coredns                   0                   69fa6a8d47f90       coredns-66bc5c9577-gd2br
9a8ce4122766e       6e38f40d628db                                                                                              16 minutes ago       Exited              storage-provisioner       0                   00da22a329b45       storage-provisioner
13cc44ba4a45f       df0860106674d                                                                                              16 minutes ago       Running             kube-proxy                0                   75f11ff42de04       kube-proxy-dr87l
5470dffe25bdb       a0af72f2ec6d6                                                                                              16 minutes ago       Running             kube-controller-manager   0                   0288c4277ae89       kube-controller-manager-minikube
848834f69ea21       90550c43ad2bc                                                                                              16 minutes ago       Running             kube-apiserver            0                   c9255e4a610bd       kube-apiserver-minikube
f4582521e3f04       46169d968e920                                                                                              16 minutes ago       Running             kube-scheduler            0                   8d1ca89f684c6       kube-scheduler-minikube
60f2ccf59ceac       5f1f5298c888d                                                                                              16 minutes ago       Running             etcd                      0                   9bfa43a0f3a99       etcd-minikube


==> coredns [ef62f38038a7] <==
[INFO] 10.244.0.3:49496 - 815 "AAAA IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000235147s
[INFO] 10.244.0.3:49496 - 317 "A IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000531839s
[INFO] 10.244.0.3:41045 - 19475 "AAAA IN registry.npmjs.org. udp 36 false 512" NOERROR qr,rd,ra 36 0.007203802s
[INFO] 10.244.0.3:41045 - 19004 "A IN registry.npmjs.org. udp 36 false 512" NOERROR qr,rd,ra 444 0.010909456s
[INFO] **********:57520 - 62982 "AAAA IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.000208535s
[INFO] **********:57520 - 62663 "A IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.000265138s
[INFO] **********:48943 - 57433 "AAAA IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.000142442s
[INFO] **********:48943 - 57223 "A IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.00011979s
[INFO] **********:35451 - 56422 "A IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000089247s
[INFO] **********:35451 - 56669 "AAAA IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000097885s
[INFO] **********:49966 - 35660 "A IN registry.npmjs.org. udp 36 false 512" NOERROR qr,aa,rd,ra 444 0.000113726s
[INFO] **********:49966 - 35871 "AAAA IN registry.npmjs.org. udp 36 false 512" NOERROR qr,rd,ra 36 0.002180596s
[INFO] 10.244.0.3:58031 - 7783 "AAAA IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.000390148s
[INFO] 10.244.0.3:58031 - 7253 "A IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.000469522s
[INFO] 10.244.0.3:35333 - 8197 "A IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.000153882s
[INFO] 10.244.0.3:35333 - 8550 "AAAA IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.000407894s
[INFO] 10.244.0.3:59654 - 9734 "AAAA IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000100796s
[INFO] 10.244.0.3:59654 - 9429 "A IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000244582s
[INFO] 10.244.0.3:36079 - 22249 "AAAA IN registry.npmjs.org. udp 36 false 512" NOERROR qr,rd,ra 36 0.006069666s
[INFO] 10.244.0.3:36079 - 21990 "A IN registry.npmjs.org. udp 36 false 512" NOERROR qr,rd,ra 444 0.008880673s
[INFO] **********:40614 - 6176 "AAAA IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.000204705s
[INFO] **********:40614 - 5951 "A IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.000246229s
[INFO] **********:53525 - 25376 "A IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.000137903s
[INFO] **********:53525 - 25607 "AAAA IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.000201409s
[INFO] **********:56479 - 44241 "AAAA IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000080969s
[INFO] **********:56479 - 43949 "A IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000118133s
[INFO] **********:46199 - 39627 "AAAA IN registry.npmjs.org. udp 36 false 512" NOERROR qr,rd,ra 36 0.010784429s
[INFO] **********:46199 - 39374 "A IN registry.npmjs.org. udp 36 false 512" NOERROR qr,rd,ra 444 0.016450027s
[INFO] 10.244.0.3:46735 - 40856 "AAAA IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.000506516s
[INFO] 10.244.0.3:46735 - 40312 "A IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.000573972s
[INFO] 10.244.0.3:58361 - 61659 "A IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.000164446s
[INFO] 10.244.0.3:58361 - 62165 "AAAA IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.000275984s
[INFO] 10.244.0.3:51071 - 20989 "AAAA IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000132214s
[INFO] 10.244.0.3:51071 - 20646 "A IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000293909s
[INFO] 10.244.0.3:55944 - 2017 "AAAA IN registry.npmjs.org. udp 36 false 512" NOERROR qr,rd,ra 36 0.003315426s
[INFO] 10.244.0.3:55944 - 1730 "A IN registry.npmjs.org. udp 36 false 512" NOERROR qr,rd,ra 444 0.007137348s
[INFO] **********:59782 - 65235 "AAAA IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.000221836s
[INFO] **********:59782 - 64774 "A IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.000310438s
[INFO] **********:53065 - 41519 "AAAA IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.000108822s
[INFO] **********:53065 - 41248 "A IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.000129085s
[INFO] **********:48961 - 29156 "AAAA IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000101761s
[INFO] **********:48961 - 28723 "A IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000187148s
[INFO] **********:59353 - 28183 "A IN registry.npmjs.org. udp 36 false 512" NOERROR qr,aa,rd,ra 444 0.020164366s
[INFO] **********:59353 - 28599 "AAAA IN registry.npmjs.org. udp 36 false 512" NOERROR qr,rd,ra 36 0.070117247s
[INFO] 10.244.0.3:53496 - 48586 "AAAA IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.000262534s
[INFO] 10.244.0.3:53496 - 48152 "A IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.000109169s
[INFO] 10.244.0.3:46415 - 48910 "A IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.000124393s
[INFO] 10.244.0.3:46415 - 49178 "AAAA IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.000075106s
[INFO] 10.244.0.3:56430 - 61854 "A IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000058217s
[INFO] 10.244.0.3:56430 - 62023 "AAAA IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000059292s
[INFO] 10.244.0.3:38615 - 42414 "AAAA IN registry.npmjs.org. udp 36 false 512" NOERROR qr,rd,ra 36 0.004997262s
[INFO] 10.244.0.3:38615 - 42232 "A IN registry.npmjs.org. udp 36 false 512" NOERROR qr,rd,ra 444 0.007648659s
[INFO] **********:57533 - 6550 "AAAA IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.00025332s
[INFO] **********:57533 - 6361 "A IN registry.npmjs.org.default.svc.cluster.local. udp 62 false 512" NXDOMAIN qr,aa,rd 155 0.000299621s
[INFO] **********:55124 - 38194 "A IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.000119853s
[INFO] **********:55124 - 38505 "AAAA IN registry.npmjs.org.svc.cluster.local. udp 54 false 512" NXDOMAIN qr,aa,rd 147 0.000206041s
[INFO] **********:50323 - 46826 "AAAA IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000132623s
[INFO] **********:50323 - 46355 "A IN registry.npmjs.org.cluster.local. udp 50 false 512" NXDOMAIN qr,aa,rd 143 0.000179901s
[INFO] **********:48273 - 40190 "A IN registry.npmjs.org. udp 36 false 512" NOERROR qr,aa,rd,ra 444 0.001181943s
[INFO] **********:48273 - 40471 "AAAA IN registry.npmjs.org. udp 36 false 512" NOERROR qr,rd,ra 36 0.011618503s


==> describe nodes <==
Name:               minikube
Roles:              control-plane
Labels:             beta.kubernetes.io/arch=amd64
                    beta.kubernetes.io/os=linux
                    kubernetes.io/arch=amd64
                    kubernetes.io/hostname=minikube
                    kubernetes.io/os=linux
                    minikube.k8s.io/commit=65318f4cfff9c12cc87ec9eb8f4cdd57b25047f3
                    minikube.k8s.io/name=minikube
                    minikube.k8s.io/primary=true
                    minikube.k8s.io/updated_at=2025_10_01T11_10_32_0700
                    minikube.k8s.io/version=v1.37.0
                    node-role.kubernetes.io/control-plane=
                    node.kubernetes.io/exclude-from-external-load-balancers=
Annotations:        node.alpha.kubernetes.io/ttl: 0
                    volumes.kubernetes.io/controller-managed-attach-detach: true
CreationTimestamp:  Wed, 01 Oct 2025 05:10:28 +0000
Taints:             <none>
Unschedulable:      false
Lease:
  HolderIdentity:  minikube
  AcquireTime:     <unset>
  RenewTime:       Wed, 01 Oct 2025 05:26:42 +0000
Conditions:
  Type             Status  LastHeartbeatTime                 LastTransitionTime                Reason                       Message
  ----             ------  -----------------                 ------------------                ------                       -------
  MemoryPressure   False   Wed, 01 Oct 2025 05:24:19 +0000   Wed, 01 Oct 2025 05:10:26 +0000   KubeletHasSufficientMemory   kubelet has sufficient memory available
  DiskPressure     False   Wed, 01 Oct 2025 05:24:19 +0000   Wed, 01 Oct 2025 05:10:26 +0000   KubeletHasNoDiskPressure     kubelet has no disk pressure
  PIDPressure      False   Wed, 01 Oct 2025 05:24:19 +0000   Wed, 01 Oct 2025 05:10:26 +0000   KubeletHasSufficientPID      kubelet has sufficient PID available
  Ready            True    Wed, 01 Oct 2025 05:24:19 +0000   Wed, 01 Oct 2025 05:10:29 +0000   KubeletReady                 kubelet is posting ready status
Addresses:
  InternalIP:  ************
  Hostname:    minikube
Capacity:
  cpu:                4
  ephemeral-storage:  1055762868Ki
  hugepages-1Gi:      0
  hugepages-2Mi:      0
  memory:             7963884Ki
  pods:               110
Allocatable:
  cpu:                4
  ephemeral-storage:  1055762868Ki
  hugepages-1Gi:      0
  hugepages-2Mi:      0
  memory:             7963884Ki
  pods:               110
System Info:
  Machine ID:                 e5b9f56c125b4af58954667496b07656
  System UUID:                e5b9f56c125b4af58954667496b07656
  Boot ID:                    142b02d6-6ddd-48fe-a621-e85e3b111ace
  Kernel Version:             ********-microsoft-standard-WSL2
  OS Image:                   Ubuntu 22.04.5 LTS
  Operating System:           linux
  Architecture:               amd64
  Container Runtime Version:  docker://28.4.0
  Kubelet Version:            v1.34.0
  Kube-Proxy Version:         
PodCIDR:                      **********/24
PodCIDRs:                     **********/24
Non-terminated Pods:          (9 in total)
  Namespace                   Name                                    CPU Requests  CPU Limits  Memory Requests  Memory Limits  Age
  ---------                   ----                                    ------------  ----------  ---------------  -------------  ---
  default                     kubernetes-demo-api-54db6ffd65-cjqpf    100m (2%)     500m (12%)  128Mi (1%)       512Mi (6%)     8m52s
  default                     kubernetes-demo-api-54db6ffd65-cp9sx    100m (2%)     500m (12%)  128Mi (1%)       512Mi (6%)     8m52s
  kube-system                 coredns-66bc5c9577-gd2br                100m (2%)     0 (0%)      70Mi (0%)        170Mi (2%)     16m
  kube-system                 etcd-minikube                           100m (2%)     0 (0%)      100Mi (1%)       0 (0%)         16m
  kube-system                 kube-apiserver-minikube                 250m (6%)     0 (0%)      0 (0%)           0 (0%)         16m
  kube-system                 kube-controller-manager-minikube        200m (5%)     0 (0%)      0 (0%)           0 (0%)         16m
  kube-system                 kube-proxy-dr87l                        0 (0%)        0 (0%)      0 (0%)           0 (0%)         16m
  kube-system                 kube-scheduler-minikube                 100m (2%)     0 (0%)      0 (0%)           0 (0%)         16m
  kube-system                 storage-provisioner                     0 (0%)        0 (0%)      0 (0%)           0 (0%)         16m
Allocated resources:
  (Total limits may be over 100 percent, i.e., overcommitted.)
  Resource           Requests    Limits
  --------           --------    ------
  cpu                950m (23%)  1 (25%)
  memory             426Mi (5%)  1194Mi (15%)
  ephemeral-storage  0 (0%)      0 (0%)
  hugepages-1Gi      0 (0%)      0 (0%)
  hugepages-2Mi      0 (0%)      0 (0%)
Events:
  Type    Reason                   Age   From             Message
  ----    ------                   ----  ----             -------
  Normal  Starting                 16m   kube-proxy       
  Normal  Starting                 16m   kubelet          Starting kubelet.
  Normal  NodeAllocatableEnforced  16m   kubelet          Updated Node Allocatable limit across pods
  Normal  NodeHasSufficientMemory  16m   kubelet          Node minikube status is now: NodeHasSufficientMemory
  Normal  NodeHasNoDiskPressure    16m   kubelet          Node minikube status is now: NodeHasNoDiskPressure
  Normal  NodeHasSufficientPID     16m   kubelet          Node minikube status is now: NodeHasSufficientPID
  Normal  RegisteredNode           16m   node-controller  Node minikube event: Registered Node minikube in Controller


==> dmesg <==
[Oct 1 04:00] Hyper-V: Disabling IBT because of Hyper-V bug
[  +0.068375] PCI: Fatal: No config space access function found
[  +0.044241] PCI: System does not support PCI
[  +0.431052] device-mapper: core: CONFIG_IMA_DISABLE_HTABLE is disabled. Duplicate IMA measurements will not be recorded in the IMA log.
[  +6.462940] hv_storvsc fd1d2cbd-ce7c-535c-966b-eb5f811c95f0: tag#255 cmd 0x2a status: scsi 0x0 srb 0x4 hv 0xc00000a1
[  +1.131763] WSL (1 - init(docker-desktop)) ERROR: ConfigApplyWindowsLibPath:2058: open /etc/ld.so.conf.d/ld.wsl.conf failed 2
[  +0.262914] WSL (1 - init(docker-desktop)) WARNING: /usr/share/zoneinfo/Asia/Dhaka not found. Is the tzdata package installed?
[  +0.016477] pulseaudio[223]: memfd_create() called without MFD_EXEC or MFD_NOEXEC_SEAL set
[  +0.774930] misc dxg: dxgk: dxgkio_is_feature_enabled: Ioctl failed: -22
[  +0.272872] misc dxg: dxgk: dxgkio_query_adapter_info: Ioctl failed: -22
[  +0.160475] misc dxg: dxgk: dxgkio_query_adapter_info: Ioctl failed: -22
[  +0.064114] misc dxg: dxgk: dxgkio_query_adapter_info: Ioctl failed: -22
[  +0.048107] misc dxg: dxgk: dxgkio_query_adapter_info: Ioctl failed: -2
[Oct 1 04:01] netlink: 'init': attribute type 4 has an invalid length.
[  +1.377100] WSL (191) ERROR: CheckConnection: getaddrinfo() failed: -5


==> etcd [60f2ccf59cea] <==
{"level":"warn","ts":"2025-10-01T05:17:30.905290Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"217.80139ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"/registry/services/endpoints/default/kubernetes\" limit:1 ","response":"range_response_count:1 size:420"}
{"level":"info","ts":"2025-10-01T05:17:30.905400Z","caller":"traceutil/trace.go:172","msg":"trace[1270014159] range","detail":"{range_begin:/registry/services/endpoints/default/kubernetes; range_end:; response_count:1; response_revision:707; }","duration":"217.928584ms","start":"2025-10-01T05:17:30.687458Z","end":"2025-10-01T05:17:30.905387Z","steps":["trace[1270014159] 'agreement among raft nodes before linearized reading'  (duration: 117.598313ms)","trace[1270014159] 'range keys from in-memory index tree'  (duration: 100.130255ms)"],"step_count":2}
{"level":"warn","ts":"2025-10-01T05:17:30.960361Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"100.32963ms","expected-duration":"100ms","prefix":"","request":"header:<ID:8128040332149286650 username:\"kube-apiserver-etcd-client\" auth_revision:1 > txn:<compare:<target:MOD key:\"/registry/services/endpoints/kube-system/k8s.io-minikube-hostpath\" mod_revision:706 > success:<request_put:<key:\"/registry/services/endpoints/kube-system/k8s.io-minikube-hostpath\" value_size:512 >> failure:<request_range:<key:\"/registry/services/endpoints/kube-system/k8s.io-minikube-hostpath\" > >>","response":"size:16"}
{"level":"info","ts":"2025-10-01T05:17:30.960497Z","caller":"traceutil/trace.go:172","msg":"trace[600495586] transaction","detail":"{read_only:false; response_revision:708; number_of_response:1; }","duration":"288.579658ms","start":"2025-10-01T05:17:30.671900Z","end":"2025-10-01T05:17:30.960480Z","steps":["trace[600495586] 'process raft request'  (duration: 133.075507ms)","trace[600495586] 'compare'  (duration: 100.220889ms)"],"step_count":2}
{"level":"warn","ts":"2025-10-01T05:17:31.152725Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"108.937748ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"/registry/mutatingwebhookconfigurations\" limit:1 ","response":"range_response_count:0 size:5"}
{"level":"info","ts":"2025-10-01T05:17:31.152790Z","caller":"traceutil/trace.go:172","msg":"trace[663584222] range","detail":"{range_begin:/registry/mutatingwebhookconfigurations; range_end:; response_count:0; response_revision:708; }","duration":"109.013549ms","start":"2025-10-01T05:17:31.043766Z","end":"2025-10-01T05:17:31.152780Z","steps":["trace[663584222] 'range keys from in-memory index tree'  (duration: 108.855245ms)"],"step_count":1}
{"level":"info","ts":"2025-10-01T05:17:35.259638Z","caller":"traceutil/trace.go:172","msg":"trace[685303602] linearizableReadLoop","detail":"{readStateIndex:803; appliedIndex:803; }","duration":"187.552557ms","start":"2025-10-01T05:17:35.072068Z","end":"2025-10-01T05:17:35.259620Z","steps":["trace[685303602] 'read index received'  (duration: 187.546532ms)","trace[685303602] 'applied index is now lower than readState.Index'  (duration: 5.245µs)"],"step_count":2}
{"level":"info","ts":"2025-10-01T05:17:35.259729Z","caller":"traceutil/trace.go:172","msg":"trace[711672734] transaction","detail":"{read_only:false; response_revision:711; number_of_response:1; }","duration":"214.225239ms","start":"2025-10-01T05:17:35.045497Z","end":"2025-10-01T05:17:35.259722Z","steps":["trace[711672734] 'process raft request'  (duration: 214.126857ms)"],"step_count":1}
{"level":"warn","ts":"2025-10-01T05:17:35.259747Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"187.684318ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"/registry/health\" ","response":"range_response_count:0 size:5"}
{"level":"info","ts":"2025-10-01T05:17:35.259767Z","caller":"traceutil/trace.go:172","msg":"trace[590212068] range","detail":"{range_begin:/registry/health; range_end:; response_count:0; response_revision:711; }","duration":"187.718687ms","start":"2025-10-01T05:17:35.072043Z","end":"2025-10-01T05:17:35.259762Z","steps":["trace[590212068] 'agreement among raft nodes before linearized reading'  (duration: 187.662309ms)"],"step_count":1}
{"level":"warn","ts":"2025-10-01T05:17:36.049114Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"429.891574ms","expected-duration":"100ms","prefix":"read-only range ","request":"limit:1 serializable:true keys_only:true ","response":"range_response_count:0 size:5"}
{"level":"warn","ts":"2025-10-01T05:17:36.049138Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"666.977589ms","expected-duration":"100ms","prefix":"read-only range ","request":"limit:1 serializable:true keys_only:true ","response":"range_response_count:0 size:5"}
{"level":"info","ts":"2025-10-01T05:17:36.049176Z","caller":"traceutil/trace.go:172","msg":"trace[**********] range","detail":"{range_begin:; range_end:; response_count:0; response_revision:711; }","duration":"429.965812ms","start":"2025-10-01T05:17:35.619200Z","end":"2025-10-01T05:17:36.049166Z","steps":["trace[**********] 'range keys from in-memory index tree'  (duration: 429.855683ms)"],"step_count":1}
{"level":"warn","ts":"2025-10-01T05:17:36.049249Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"733.655846ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"/registry/resourceslices\" limit:1 ","response":"range_response_count:0 size:5"}
{"level":"info","ts":"2025-10-01T05:17:36.049178Z","caller":"traceutil/trace.go:172","msg":"trace[9729740] range","detail":"{range_begin:; range_end:; response_count:0; response_revision:711; }","duration":"667.024048ms","start":"2025-10-01T05:17:35.382147Z","end":"2025-10-01T05:17:36.049171Z","steps":["trace[9729740] 'range keys from in-memory index tree'  (duration: 666.946754ms)"],"step_count":1}
{"level":"info","ts":"2025-10-01T05:17:36.049264Z","caller":"traceutil/trace.go:172","msg":"trace[1141827122] range","detail":"{range_begin:/registry/resourceslices; range_end:; response_count:0; response_revision:711; }","duration":"733.671652ms","start":"2025-10-01T05:17:35.315588Z","end":"2025-10-01T05:17:36.049259Z","steps":["trace[1141827122] 'range keys from in-memory index tree'  (duration: 733.593257ms)"],"step_count":1}
{"level":"warn","ts":"2025-10-01T05:17:36.049283Z","caller":"v3rpc/interceptor.go:202","msg":"request stats","start time":"2025-10-01T05:17:35.315569Z","time spent":"733.707913ms","remote":"127.0.0.1:33990","response type":"/etcdserverpb.KV/Range","request count":0,"request size":28,"response count":0,"response size":29,"request content":"key:\"/registry/resourceslices\" limit:1 "}
{"level":"warn","ts":"2025-10-01T05:17:36.365830Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"499.386766ms","expected-duration":"100ms","prefix":"","request":"header:<ID:8128040332149286676 > lease_revoke:<id:70cc999e2dce66c1>","response":"size:29"}
{"level":"info","ts":"2025-10-01T05:17:36.365907Z","caller":"traceutil/trace.go:172","msg":"trace[383785311] linearizableReadLoop","detail":"{readStateIndex:805; appliedIndex:804; }","duration":"316.61343ms","start":"2025-10-01T05:17:36.049283Z","end":"2025-10-01T05:17:36.365896Z","steps":["trace[383785311] 'read index received'  (duration: 58.82µs)","trace[383785311] 'applied index is now lower than readState.Index'  (duration: 316.553994ms)"],"step_count":2}
{"level":"warn","ts":"2025-10-01T05:17:36.365979Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"293.841357ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"/registry/health\" ","response":"range_response_count:0 size:5"}
{"level":"warn","ts":"2025-10-01T05:17:36.365977Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"316.685472ms","expected-duration":"100ms","prefix":"read-only range ","request":"limit:1 keys_only:true ","response":"range_response_count:0 size:5"}
{"level":"info","ts":"2025-10-01T05:17:36.366007Z","caller":"traceutil/trace.go:172","msg":"trace[**********] range","detail":"{range_begin:; range_end:; response_count:0; response_revision:711; }","duration":"316.721947ms","start":"2025-10-01T05:17:36.049278Z","end":"2025-10-01T05:17:36.366000Z","steps":["trace[**********] 'agreement among raft nodes before linearized reading'  (duration: 316.669713ms)"],"step_count":1}
{"level":"info","ts":"2025-10-01T05:17:36.366016Z","caller":"traceutil/trace.go:172","msg":"trace[**********] range","detail":"{range_begin:/registry/health; range_end:; response_count:0; response_revision:711; }","duration":"293.857703ms","start":"2025-10-01T05:17:36.072131Z","end":"2025-10-01T05:17:36.365989Z","steps":["trace[**********] 'agreement among raft nodes before linearized reading'  (duration: 293.825277ms)"],"step_count":1}
{"level":"info","ts":"2025-10-01T05:17:37.457135Z","caller":"traceutil/trace.go:172","msg":"trace[183626359] transaction","detail":"{read_only:false; response_revision:712; number_of_response:1; }","duration":"190.175697ms","start":"2025-10-01T05:17:37.266945Z","end":"2025-10-01T05:17:37.457121Z","steps":["trace[183626359] 'process raft request'  (duration: 190.062559ms)"],"step_count":1}
{"level":"info","ts":"2025-10-01T05:17:39.741188Z","caller":"traceutil/trace.go:172","msg":"trace[**********] transaction","detail":"{read_only:false; response_revision:714; number_of_response:1; }","duration":"150.082324ms","start":"2025-10-01T05:17:39.591093Z","end":"2025-10-01T05:17:39.741175Z","steps":["trace[**********] 'process raft request'  (duration: 149.814592ms)"],"step_count":1}
{"level":"warn","ts":"2025-10-01T05:17:41.351997Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"290.253242ms","expected-duration":"100ms","prefix":"","request":"header:<ID:8128040332149286704 username:\"kube-apiserver-etcd-client\" auth_revision:1 > txn:<compare:<target:MOD key:\"/registry/masterleases/************\" mod_revision:707 > success:<request_put:<key:\"/registry/masterleases/************\" value_size:65 lease:8128040332149286702 >> failure:<request_range:<key:\"/registry/masterleases/************\" > >>","response":"size:16"}
{"level":"info","ts":"2025-10-01T05:17:41.352076Z","caller":"traceutil/trace.go:172","msg":"trace[2112372850] linearizableReadLoop","detail":"{readStateIndex:810; appliedIndex:809; }","duration":"279.017787ms","start":"2025-10-01T05:17:41.073049Z","end":"2025-10-01T05:17:41.352067Z","steps":["trace[2112372850] 'read index received'  (duration: 27.599µs)","trace[2112372850] 'applied index is now lower than readState.Index'  (duration: 278.989426ms)"],"step_count":2}
{"level":"info","ts":"2025-10-01T05:17:41.352132Z","caller":"traceutil/trace.go:172","msg":"trace[799265438] transaction","detail":"{read_only:false; response_revision:715; number_of_response:1; }","duration":"471.422382ms","start":"2025-10-01T05:17:40.880704Z","end":"2025-10-01T05:17:41.352126Z","steps":["trace[799265438] 'process raft request'  (duration: 180.989435ms)","trace[799265438] 'compare'  (duration: 290.097424ms)"],"step_count":2}
{"level":"warn","ts":"2025-10-01T05:17:41.352174Z","caller":"v3rpc/interceptor.go:202","msg":"request stats","start time":"2025-10-01T05:17:40.880685Z","time spent":"471.463966ms","remote":"127.0.0.1:32980","response type":"/etcdserverpb.KV/Txn","request count":1,"request size":116,"response count":0,"response size":40,"request content":"compare:<target:MOD key:\"/registry/masterleases/************\" mod_revision:707 > success:<request_put:<key:\"/registry/masterleases/************\" value_size:65 lease:8128040332149286702 >> failure:<request_range:<key:\"/registry/masterleases/************\" > >"}
{"level":"warn","ts":"2025-10-01T05:17:41.352361Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"279.326232ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"/registry/health\" ","response":"range_response_count:0 size:5"}
{"level":"info","ts":"2025-10-01T05:17:41.352389Z","caller":"traceutil/trace.go:172","msg":"trace[**********] range","detail":"{range_begin:/registry/health; range_end:; response_count:0; response_revision:715; }","duration":"279.356569ms","start":"2025-10-01T05:17:41.073027Z","end":"2025-10-01T05:17:41.352383Z","steps":["trace[**********] 'agreement among raft nodes before linearized reading'  (duration: 279.307853ms)"],"step_count":1}
{"level":"warn","ts":"2025-10-01T05:17:41.352428Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"229.347541ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"/registry/events/\" range_end:\"/registry/events0\" keys_only:true ","response":"range_response_count:27 size:2243"}
{"level":"info","ts":"2025-10-01T05:17:41.352502Z","caller":"traceutil/trace.go:172","msg":"trace[**********] range","detail":"{range_begin:/registry/events/; range_end:/registry/events0; response_count:27; response_revision:715; }","duration":"229.424932ms","start":"2025-10-01T05:17:41.123070Z","end":"2025-10-01T05:17:41.352495Z","steps":["trace[**********] 'agreement among raft nodes before linearized reading'  (duration: 229.202786ms)"],"step_count":1}
{"level":"info","ts":"2025-10-01T05:17:41.874475Z","caller":"traceutil/trace.go:172","msg":"trace[2000183746] transaction","detail":"{read_only:false; response_revision:716; number_of_response:1; }","duration":"120.498929ms","start":"2025-10-01T05:17:41.753960Z","end":"2025-10-01T05:17:41.874459Z","steps":["trace[2000183746] 'process raft request'  (duration: 120.368363ms)"],"step_count":1}
{"level":"warn","ts":"2025-10-01T05:17:42.604390Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"222.25472ms","expected-duration":"100ms","prefix":"read-only range ","request":"limit:1 keys_only:true ","response":"range_response_count:0 size:5"}
{"level":"info","ts":"2025-10-01T05:17:42.604451Z","caller":"traceutil/trace.go:172","msg":"trace[758829530] range","detail":"{range_begin:; range_end:; response_count:0; response_revision:716; }","duration":"222.326439ms","start":"2025-10-01T05:17:42.382112Z","end":"2025-10-01T05:17:42.604439Z","steps":["trace[758829530] 'agreement among raft nodes before linearized reading'  (duration: 27.377403ms)","trace[758829530] 'range keys from in-memory index tree'  (duration: 194.845006ms)"],"step_count":2}
{"level":"warn","ts":"2025-10-01T05:17:42.604477Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"568.506191ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"/registry/runtimeclasses\" limit:1 ","response":"range_response_count:0 size:5"}
{"level":"info","ts":"2025-10-01T05:17:42.604509Z","caller":"traceutil/trace.go:172","msg":"trace[1194080394] range","detail":"{range_begin:/registry/runtimeclasses; range_end:; response_count:0; response_revision:716; }","duration":"568.543206ms","start":"2025-10-01T05:17:42.035959Z","end":"2025-10-01T05:17:42.604502Z","steps":["trace[1194080394] 'range keys from in-memory index tree'  (duration: 568.457607ms)"],"step_count":1}
{"level":"warn","ts":"2025-10-01T05:17:42.604530Z","caller":"v3rpc/interceptor.go:202","msg":"request stats","start time":"2025-10-01T05:17:42.035943Z","time spent":"568.581459ms","remote":"127.0.0.1:33562","response type":"/etcdserverpb.KV/Range","request count":0,"request size":28,"response count":0,"response size":29,"request content":"key:\"/registry/runtimeclasses\" limit:1 "}
{"level":"warn","ts":"2025-10-01T05:17:42.604701Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"195.135035ms","expected-duration":"100ms","prefix":"","request":"header:<ID:8128040332149286715 username:\"kube-apiserver-etcd-client\" auth_revision:1 > txn:<compare:<target:MOD key:\"/registry/leases/kube-node-lease/minikube\" mod_revision:709 > success:<request_put:<key:\"/registry/leases/kube-node-lease/minikube\" value_size:471 >> failure:<request_range:<key:\"/registry/leases/kube-node-lease/minikube\" > >>","response":"size:16"}
{"level":"info","ts":"2025-10-01T05:17:42.604742Z","caller":"traceutil/trace.go:172","msg":"trace[826172195] linearizableReadLoop","detail":"{readStateIndex:812; appliedIndex:811; }","duration":"165.834878ms","start":"2025-10-01T05:17:42.438902Z","end":"2025-10-01T05:17:42.604736Z","steps":["trace[826172195] 'read index received'  (duration: 30.235µs)","trace[826172195] 'applied index is now lower than readState.Index'  (duration: 165.804067ms)"],"step_count":2}
{"level":"info","ts":"2025-10-01T05:17:42.604851Z","caller":"traceutil/trace.go:172","msg":"trace[632869898] transaction","detail":"{read_only:false; response_revision:717; number_of_response:1; }","duration":"480.729563ms","start":"2025-10-01T05:17:42.124115Z","end":"2025-10-01T05:17:42.604845Z","steps":["trace[632869898] 'process raft request'  (duration: 285.410562ms)","trace[632869898] 'compare'  (duration: 194.962273ms)"],"step_count":2}
{"level":"warn","ts":"2025-10-01T05:17:42.604894Z","caller":"v3rpc/interceptor.go:202","msg":"request stats","start time":"2025-10-01T05:17:42.124094Z","time spent":"480.776751ms","remote":"127.0.0.1:33424","response type":"/etcdserverpb.KV/Txn","request count":1,"request size":520,"response count":0,"response size":40,"request content":"compare:<target:MOD key:\"/registry/leases/kube-node-lease/minikube\" mod_revision:709 > success:<request_put:<key:\"/registry/leases/kube-node-lease/minikube\" value_size:471 >> failure:<request_range:<key:\"/registry/leases/kube-node-lease/minikube\" > >"}
{"level":"warn","ts":"2025-10-01T05:17:42.605192Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"166.30314ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"/registry/clusterroles/\" range_end:\"/registry/clusterroles0\" limit:10000 revision:714 ","response":"range_response_count:70 size:64074"}
{"level":"info","ts":"2025-10-01T05:17:42.605229Z","caller":"traceutil/trace.go:172","msg":"trace[868104557] range","detail":"{range_begin:/registry/clusterroles/; range_end:/registry/clusterroles0; response_count:70; response_revision:717; }","duration":"166.343717ms","start":"2025-10-01T05:17:42.438880Z","end":"2025-10-01T05:17:42.605224Z","steps":["trace[868104557] 'agreement among raft nodes before linearized reading'  (duration: 166.092824ms)"],"step_count":1}
{"level":"warn","ts":"2025-10-01T05:17:42.605353Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"101.653624ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"/registry/mutatingwebhookconfigurations\" limit:1 ","response":"range_response_count:0 size:5"}
{"level":"info","ts":"2025-10-01T05:17:42.605368Z","caller":"traceutil/trace.go:172","msg":"trace[2102068034] range","detail":"{range_begin:/registry/mutatingwebhookconfigurations; range_end:; response_count:0; response_revision:717; }","duration":"101.670245ms","start":"2025-10-01T05:17:42.503694Z","end":"2025-10-01T05:17:42.605365Z","steps":["trace[2102068034] 'agreement among raft nodes before linearized reading'  (duration: 101.642006ms)"],"step_count":1}
{"level":"info","ts":"2025-10-01T05:17:44.121606Z","caller":"traceutil/trace.go:172","msg":"trace[431089727] linearizableReadLoop","detail":"{readStateIndex:812; appliedIndex:812; }","duration":"176.603898ms","start":"2025-10-01T05:17:43.944985Z","end":"2025-10-01T05:17:44.121588Z","steps":["trace[431089727] 'read index received'  (duration: 176.596923ms)","trace[431089727] 'applied index is now lower than readState.Index'  (duration: 6.099µs)"],"step_count":2}
{"level":"warn","ts":"2025-10-01T05:17:44.121745Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"176.758211ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"/registry/podtemplates\" limit:1 ","response":"range_response_count:0 size:5"}
{"level":"info","ts":"2025-10-01T05:17:44.121767Z","caller":"traceutil/trace.go:172","msg":"trace[739387733] range","detail":"{range_begin:/registry/podtemplates; range_end:; response_count:0; response_revision:717; }","duration":"176.79655ms","start":"2025-10-01T05:17:43.944964Z","end":"2025-10-01T05:17:44.121760Z","steps":["trace[739387733] 'agreement among raft nodes before linearized reading'  (duration: 176.723456ms)"],"step_count":1}
{"level":"info","ts":"2025-10-01T05:17:44.121740Z","caller":"traceutil/trace.go:172","msg":"trace[1220802488] transaction","detail":"{read_only:false; response_revision:718; number_of_response:1; }","duration":"240.26516ms","start":"2025-10-01T05:17:43.881468Z","end":"2025-10-01T05:17:44.121733Z","steps":["trace[1220802488] 'process raft request'  (duration: 240.161893ms)"],"step_count":1}
{"level":"warn","ts":"2025-10-01T05:17:44.402016Z","caller":"txn/util.go:93","msg":"apply request took too long","took":"168.403266ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"/registry/poddisruptionbudgets/\" range_end:\"/registry/poddisruptionbudgets0\" limit:10000 revision:717 ","response":"range_response_count:0 size:5"}
{"level":"info","ts":"2025-10-01T05:17:44.402078Z","caller":"traceutil/trace.go:172","msg":"trace[1092521829] range","detail":"{range_begin:/registry/poddisruptionbudgets/; range_end:/registry/poddisruptionbudgets0; response_count:0; response_revision:718; }","duration":"168.482226ms","start":"2025-10-01T05:17:44.233585Z","end":"2025-10-01T05:17:44.402067Z","steps":["trace[1092521829] 'range keys from in-memory index tree'  (duration: 168.340422ms)"],"step_count":1}
{"level":"info","ts":"2025-10-01T05:17:50.788847Z","caller":"traceutil/trace.go:172","msg":"trace[2081164710] transaction","detail":"{read_only:false; response_revision:723; number_of_response:1; }","duration":"105.461218ms","start":"2025-10-01T05:17:50.683363Z","end":"2025-10-01T05:17:50.788824Z","steps":["trace[2081164710] 'process raft request'  (duration: 40.378247ms)","trace[2081164710] 'compare'  (duration: 64.089477ms)"],"step_count":2}
{"level":"info","ts":"2025-10-01T05:20:26.955985Z","caller":"mvcc/index.go:194","msg":"compact tree index","revision":608}
{"level":"info","ts":"2025-10-01T05:20:26.967318Z","caller":"mvcc/kvstore_compaction.go:70","msg":"finished scheduled compaction","compact-revision":608,"took":"9.780859ms","hash":4218656217,"current-db-size-bytes":1691648,"current-db-size":"1.7 MB","current-db-size-in-use-bytes":1691648,"current-db-size-in-use":"1.7 MB"}
{"level":"info","ts":"2025-10-01T05:20:26.967451Z","caller":"mvcc/hash.go:157","msg":"storing new hash","hash":4218656217,"revision":608,"compact-revision":-1}
{"level":"info","ts":"2025-10-01T05:25:26.935639Z","caller":"mvcc/index.go:194","msg":"compact tree index","revision":958}
{"level":"info","ts":"2025-10-01T05:25:26.940309Z","caller":"mvcc/kvstore_compaction.go:70","msg":"finished scheduled compaction","compact-revision":958,"took":"4.272443ms","hash":3759758374,"current-db-size-bytes":1691648,"current-db-size":"1.7 MB","current-db-size-in-use-bytes":1581056,"current-db-size-in-use":"1.6 MB"}
{"level":"info","ts":"2025-10-01T05:25:26.940374Z","caller":"mvcc/hash.go:157","msg":"storing new hash","hash":3759758374,"revision":958,"compact-revision":608}


==> kernel <==
 05:26:45 up  1:25,  0 users,  load average: 0.28, 0.47, 0.41
Linux minikube ********-microsoft-standard-WSL2 #1 SMP PREEMPT_DYNAMIC Thu Jun  5 18:30:46 UTC 2025 x86_64 x86_64 x86_64 GNU/Linux
PRETTY_NAME="Ubuntu 22.04.5 LTS"


==> kube-apiserver [848834f69ea2] <==
I1001 05:10:28.912157       1 shared_informer.go:356] "Caches are synced" controller="cluster_authentication_trust_controller"
I1001 05:10:28.912829       1 cache.go:39] Caches are synced for APIServiceRegistrationController controller
I1001 05:10:28.912926       1 shared_informer.go:356] "Caches are synced" controller="node_authorizer"
I1001 05:10:28.914065       1 controller.go:667] quota admission added evaluator for: leases.coordination.k8s.io
I1001 05:10:28.914093       1 handler_discovery.go:451] Starting ResourceDiscoveryManager
I1001 05:10:28.915619       1 default_servicecidr_controller.go:228] Setting default ServiceCIDR condition Ready to True
I1001 05:10:28.919454       1 cidrallocator.go:301] created ClusterIP allocator for Service CIDR *********/12
I1001 05:10:28.922466       1 apf_controller.go:382] Running API Priority and Fairness config worker
I1001 05:10:28.923124       1 apf_controller.go:385] Running API Priority and Fairness periodic rebalancing process
I1001 05:10:28.988144       1 cache.go:39] Caches are synced for RemoteAvailability controller
I1001 05:10:28.988542       1 cidrallocator.go:277] updated ClusterIP allocator for Service CIDR *********/12
I1001 05:10:28.988976       1 cache.go:39] Caches are synced for LocalAvailability controller
I1001 05:10:28.990555       1 default_servicecidr_controller.go:137] Shutting down kubernetes-service-cidr-controller
I1001 05:10:29.001738       1 cache.go:39] Caches are synced for autoregister controller
I1001 05:10:29.822866       1 storage_scheduling.go:95] created PriorityClass system-node-critical with value 2000001000
I1001 05:10:29.827313       1 storage_scheduling.go:95] created PriorityClass system-cluster-critical with value 2000000000
I1001 05:10:29.827351       1 storage_scheduling.go:111] all system priority classes are created successfully or already exist.
I1001 05:10:30.438492       1 controller.go:667] quota admission added evaluator for: roles.rbac.authorization.k8s.io
I1001 05:10:30.491666       1 controller.go:667] quota admission added evaluator for: rolebindings.rbac.authorization.k8s.io
I1001 05:10:30.644026       1 alloc.go:328] "allocated clusterIPs" service="default/kubernetes" clusterIPs={"IPv4":"*********"}
W1001 05:10:30.653251       1 lease.go:265] Resetting endpoints for master service "kubernetes" to [************]
I1001 05:10:30.654673       1 controller.go:667] quota admission added evaluator for: endpoints
I1001 05:10:30.660634       1 controller.go:667] quota admission added evaluator for: endpointslices.discovery.k8s.io
I1001 05:10:30.938422       1 controller.go:667] quota admission added evaluator for: serviceaccounts
I1001 05:10:31.638875       1 controller.go:667] quota admission added evaluator for: deployments.apps
I1001 05:10:31.648011       1 alloc.go:328] "allocated clusterIPs" service="kube-system/kube-dns" clusterIPs={"IPv4":"*********0"}
I1001 05:10:31.661900       1 controller.go:667] quota admission added evaluator for: daemonsets.apps
I1001 05:10:35.924060       1 controller.go:667] quota admission added evaluator for: controllerrevisions.apps
I1001 05:10:36.528737       1 controller.go:667] quota admission added evaluator for: replicasets.apps
I1001 05:10:36.634724       1 cidrallocator.go:277] updated ClusterIP allocator for Service CIDR *********/12
I1001 05:10:36.646369       1 cidrallocator.go:277] updated ClusterIP allocator for Service CIDR *********/12
I1001 05:11:27.751634       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:11:30.299222       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:12:32.212811       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:12:51.312534       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:13:39.850383       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:14:12.737526       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:14:46.936277       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:15:17.444907       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:15:59.909196       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:16:36.095193       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:17:03.381577       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:17:48.625405       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:17:53.706850       1 alloc.go:328] "allocated clusterIPs" service="default/devops-kubernetes-api-service" clusterIPs={"IPv4":"**************"}
I1001 05:18:24.776708       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:18:49.661141       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:19:48.231212       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:19:51.024091       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:20:28.777527       1 cidrallocator.go:277] updated ClusterIP allocator for Service CIDR *********/12
I1001 05:20:57.322787       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:21:04.935713       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:21:58.772021       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:22:34.105374       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:23:01.259769       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:23:55.507375       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:24:06.765546       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:25:13.196224       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:25:21.642861       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:26:38.726257       1 stats.go:136] "Error getting keys" err="empty key: \"\""
I1001 05:26:42.512416       1 stats.go:136] "Error getting keys" err="empty key: \"\""


==> kube-controller-manager [5470dffe25bd] <==
I1001 05:10:35.673326       1 shared_informer.go:349] "Waiting for caches to sync" controller="ReplicaSet"
I1001 05:10:35.824562       1 controllermanager.go:781] "Started controller" controller="replicationcontroller-controller"
I1001 05:10:35.824794       1 replica_set.go:243] "Starting controller" logger="replicationcontroller-controller" name="replicationcontroller"
I1001 05:10:35.824825       1 shared_informer.go:349] "Waiting for caches to sync" controller="ReplicationController"
I1001 05:10:35.832326       1 shared_informer.go:349] "Waiting for caches to sync" controller="resource quota"
I1001 05:10:35.853338       1 actual_state_of_world.go:541] "Failed to update statusUpdateNeeded field in actual state of world" logger="persistentvolume-attach-detach-controller" err="Failed to set statusUpdateNeeded to needed true, because nodeName=\"minikube\" does not exist"
I1001 05:10:35.854480       1 shared_informer.go:349] "Waiting for caches to sync" controller="garbage collector"
I1001 05:10:35.860656       1 shared_informer.go:356] "Caches are synced" controller="resource quota"
I1001 05:10:35.867848       1 shared_informer.go:356] "Caches are synced" controller="daemon sets"
I1001 05:10:35.871217       1 shared_informer.go:356] "Caches are synced" controller="HPA"
I1001 05:10:35.871474       1 shared_informer.go:356] "Caches are synced" controller="certificate-csrsigning-kubelet-serving"
I1001 05:10:35.872750       1 shared_informer.go:356] "Caches are synced" controller="certificate-csrsigning-kube-apiserver-client"
I1001 05:10:35.872790       1 shared_informer.go:356] "Caches are synced" controller="certificate-csrsigning-legacy-unknown"
I1001 05:10:35.872794       1 shared_informer.go:356] "Caches are synced" controller="certificate-csrsigning-kubelet-client"
I1001 05:10:35.872770       1 shared_informer.go:356] "Caches are synced" controller="cronjob"
I1001 05:10:35.872849       1 shared_informer.go:356] "Caches are synced" controller="garbage collector"
I1001 05:10:35.872856       1 garbagecollector.go:154] "Garbage collector: all resource monitors have synced" logger="garbage-collector-controller"
I1001 05:10:35.872862       1 garbagecollector.go:157] "Proceeding to collect garbage" logger="garbage-collector-controller"
I1001 05:10:35.874289       1 shared_informer.go:356] "Caches are synced" controller="ReplicaSet"
I1001 05:10:35.874350       1 shared_informer.go:356] "Caches are synced" controller="deployment"
I1001 05:10:35.874968       1 shared_informer.go:356] "Caches are synced" controller="service account"
I1001 05:10:35.877416       1 shared_informer.go:356] "Caches are synced" controller="service-cidr-controller"
I1001 05:10:35.877485       1 shared_informer.go:356] "Caches are synced" controller="endpoint_slice"
I1001 05:10:35.877486       1 shared_informer.go:356] "Caches are synced" controller="crt configmap"
I1001 05:10:35.878833       1 shared_informer.go:356] "Caches are synced" controller="disruption"
I1001 05:10:35.880046       1 shared_informer.go:356] "Caches are synced" controller="VAC protection"
I1001 05:10:35.885381       1 shared_informer.go:356] "Caches are synced" controller="node"
I1001 05:10:35.885530       1 range_allocator.go:177] "Sending events to api server" logger="node-ipam-controller"
I1001 05:10:35.885565       1 range_allocator.go:183] "Starting range CIDR allocator" logger="node-ipam-controller"
I1001 05:10:35.885570       1 shared_informer.go:349] "Waiting for caches to sync" controller="cidrallocator"
I1001 05:10:35.885575       1 shared_informer.go:356] "Caches are synced" controller="cidrallocator"
I1001 05:10:35.885583       1 shared_informer.go:356] "Caches are synced" controller="TTL"
I1001 05:10:35.890890       1 range_allocator.go:428] "Set node PodCIDR" logger="node-ipam-controller" node="minikube" podCIDRs=["**********/24"]
I1001 05:10:35.890992       1 shared_informer.go:356] "Caches are synced" controller="ephemeral"
I1001 05:10:35.893282       1 shared_informer.go:356] "Caches are synced" controller="attach detach"
I1001 05:10:35.901694       1 shared_informer.go:356] "Caches are synced" controller="PVC protection"
I1001 05:10:35.901732       1 shared_informer.go:356] "Caches are synced" controller="ClusterRoleAggregator"
I1001 05:10:35.908606       1 shared_informer.go:356] "Caches are synced" controller="endpoint"
I1001 05:10:35.914426       1 shared_informer.go:356] "Caches are synced" controller="certificate-csrapproving"
I1001 05:10:35.922106       1 shared_informer.go:356] "Caches are synced" controller="persistent volume"
I1001 05:10:35.922156       1 shared_informer.go:356] "Caches are synced" controller="taint-eviction-controller"
I1001 05:10:35.923385       1 shared_informer.go:356] "Caches are synced" controller="taint"
I1001 05:10:35.923407       1 shared_informer.go:356] "Caches are synced" controller="validatingadmissionpolicy-status"
I1001 05:10:35.923496       1 node_lifecycle_controller.go:1221] "Initializing eviction metric for zone" logger="node-lifecycle-controller" zone=""
I1001 05:10:35.923572       1 node_lifecycle_controller.go:873] "Missing timestamp for Node. Assuming now as a timestamp" logger="node-lifecycle-controller" node="minikube"
I1001 05:10:35.923600       1 node_lifecycle_controller.go:1067] "Controller detected that zone is now in new state" logger="node-lifecycle-controller" zone="" newState="Normal"
I1001 05:10:35.925531       1 shared_informer.go:356] "Caches are synced" controller="ReplicationController"
I1001 05:10:35.926698       1 shared_informer.go:356] "Caches are synced" controller="legacy-service-account-token-cleaner"
I1001 05:10:35.926731       1 shared_informer.go:356] "Caches are synced" controller="expand"
I1001 05:10:35.926769       1 shared_informer.go:356] "Caches are synced" controller="job"
I1001 05:10:35.926779       1 shared_informer.go:356] "Caches are synced" controller="namespace"
I1001 05:10:35.926880       1 shared_informer.go:356] "Caches are synced" controller="resource_claim"
I1001 05:10:35.926999       1 shared_informer.go:356] "Caches are synced" controller="bootstrap_signer"
I1001 05:10:35.927022       1 shared_informer.go:356] "Caches are synced" controller="TTL after finished"
I1001 05:10:35.928582       1 shared_informer.go:356] "Caches are synced" controller="endpoint_slice_mirroring"
I1001 05:10:35.930102       1 shared_informer.go:356] "Caches are synced" controller="PV protection"
I1001 05:10:35.934231       1 shared_informer.go:356] "Caches are synced" controller="stateful set"
I1001 05:10:35.938797       1 shared_informer.go:356] "Caches are synced" controller="resource quota"
I1001 05:10:35.940016       1 shared_informer.go:356] "Caches are synced" controller="GC"
I1001 05:10:35.954980       1 shared_informer.go:356] "Caches are synced" controller="garbage collector"


==> kube-proxy [13cc44ba4a45] <==
I1001 05:10:37.491650       1 server_linux.go:53] "Using iptables proxy"
I1001 05:10:37.587764       1 shared_informer.go:349] "Waiting for caches to sync" controller="node informer cache"
I1001 05:10:37.688170       1 shared_informer.go:356] "Caches are synced" controller="node informer cache"
I1001 05:10:37.688223       1 server.go:219] "Successfully retrieved NodeIPs" NodeIPs=["************"]
E1001 05:10:37.688299       1 server.go:256] "Kube-proxy configuration may be incomplete or incorrect" err="nodePortAddresses is unset; NodePort connections will be accepted on all local IPs. Consider using `--nodeport-addresses primary`"
I1001 05:10:37.704903       1 server.go:265] "kube-proxy running in dual-stack mode" primary ipFamily="IPv4"
I1001 05:10:37.704998       1 server_linux.go:132] "Using iptables Proxier"
I1001 05:10:37.711099       1 proxier.go:242] "Setting route_localnet=1 to allow node-ports on localhost; to change this either disable iptables.localhostNodePorts (--iptables-localhost-nodeports) or set nodePortAddresses (--nodeport-addresses) to filter loopback addresses" ipFamily="IPv4"
I1001 05:10:37.714694       1 server.go:527] "Version info" version="v1.34.0"
I1001 05:10:37.714725       1 server.go:529] "Golang settings" GOGC="" GOMAXPROCS="" GOTRACEBACK=""
I1001 05:10:37.716426       1 config.go:200] "Starting service config controller"
I1001 05:10:37.716451       1 shared_informer.go:349] "Waiting for caches to sync" controller="service config"
I1001 05:10:37.716466       1 config.go:106] "Starting endpoint slice config controller"
I1001 05:10:37.716470       1 shared_informer.go:349] "Waiting for caches to sync" controller="endpoint slice config"
I1001 05:10:37.716478       1 config.go:403] "Starting serviceCIDR config controller"
I1001 05:10:37.716482       1 shared_informer.go:349] "Waiting for caches to sync" controller="serviceCIDR config"
I1001 05:10:37.717804       1 config.go:309] "Starting node config controller"
I1001 05:10:37.717978       1 shared_informer.go:349] "Waiting for caches to sync" controller="node config"
I1001 05:10:37.718046       1 shared_informer.go:356] "Caches are synced" controller="node config"
I1001 05:10:37.817290       1 shared_informer.go:356] "Caches are synced" controller="serviceCIDR config"
I1001 05:10:37.817288       1 shared_informer.go:356] "Caches are synced" controller="service config"
I1001 05:10:37.817317       1 shared_informer.go:356] "Caches are synced" controller="endpoint slice config"


==> kube-scheduler [f4582521e3f0] <==
I1001 05:10:26.876477       1 serving.go:386] Generated self-signed cert in-memory
W1001 05:10:28.805079       1 requestheader_controller.go:204] Unable to get configmap/extension-apiserver-authentication in kube-system.  Usually fixed by 'kubectl create rolebinding -n kube-system ROLEBINDING_NAME --role=extension-apiserver-authentication-reader --serviceaccount=YOUR_NS:YOUR_SA'
W1001 05:10:28.805168       1 authentication.go:397] Error looking up in-cluster authentication configuration: configmaps "extension-apiserver-authentication" is forbidden: User "system:kube-scheduler" cannot get resource "configmaps" in API group "" in the namespace "kube-system"
W1001 05:10:28.805189       1 authentication.go:398] Continuing without authentication configuration. This may treat all requests as anonymous.
W1001 05:10:28.805206       1 authentication.go:399] To require authentication configuration lookup to succeed, set --authentication-tolerate-lookup-failure=false
I1001 05:10:28.918829       1 server.go:175] "Starting Kubernetes Scheduler" version="v1.34.0"
I1001 05:10:28.919043       1 server.go:177] "Golang settings" GOGC="" GOMAXPROCS="" GOTRACEBACK=""
I1001 05:10:28.921235       1 configmap_cafile_content.go:205] "Starting controller" name="client-ca::kube-system::extension-apiserver-authentication::client-ca-file"
I1001 05:10:28.921336       1 shared_informer.go:349] "Waiting for caches to sync" controller="client-ca::kube-system::extension-apiserver-authentication::client-ca-file"
I1001 05:10:28.922038       1 secure_serving.go:211] Serving securely on 127.0.0.1:10259
E1001 05:10:28.924727       1 reflector.go:205] "Failed to watch" err="failed to list *v1.CSIStorageCapacity: csistoragecapacities.storage.k8s.io is forbidden: User \"system:kube-scheduler\" cannot list resource \"csistoragecapacities\" in API group \"storage.k8s.io\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.CSIStorageCapacity"
E1001 05:10:28.924755       1 reflector.go:205] "Failed to watch" err="failed to list *v1.DeviceClass: deviceclasses.resource.k8s.io is forbidden: User \"system:kube-scheduler\" cannot list resource \"deviceclasses\" in API group \"resource.k8s.io\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.DeviceClass"
E1001 05:10:28.925002       1 reflector.go:205] "Failed to watch" err="failed to list *v1.ConfigMap: configmaps \"extension-apiserver-authentication\" is forbidden: User \"system:kube-scheduler\" cannot list resource \"configmaps\" in API group \"\" in the namespace \"kube-system\"" logger="UnhandledError" reflector="runtime/asm_amd64.s:1700" type="*v1.ConfigMap"
E1001 05:10:28.925138       1 reflector.go:205] "Failed to watch" err="failed to list *v1.VolumeAttachment: volumeattachments.storage.k8s.io is forbidden: User \"system:kube-scheduler\" cannot list resource \"volumeattachments\" in API group \"storage.k8s.io\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.VolumeAttachment"
I1001 05:10:28.922130       1 tlsconfig.go:243] "Starting DynamicServingCertificateController"
E1001 05:10:28.930961       1 reflector.go:205] "Failed to watch" err="failed to list *v1.StorageClass: storageclasses.storage.k8s.io is forbidden: User \"system:kube-scheduler\" cannot list resource \"storageclasses\" in API group \"storage.k8s.io\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.StorageClass"
E1001 05:10:28.931661       1 reflector.go:205] "Failed to watch" err="failed to list *v1.ReplicaSet: replicasets.apps is forbidden: User \"system:kube-scheduler\" cannot list resource \"replicasets\" in API group \"apps\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.ReplicaSet"
E1001 05:10:28.932346       1 reflector.go:205] "Failed to watch" err="failed to list *v1.StatefulSet: statefulsets.apps is forbidden: User \"system:kube-scheduler\" cannot list resource \"statefulsets\" in API group \"apps\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.StatefulSet"
E1001 05:10:28.932217       1 reflector.go:205] "Failed to watch" err="failed to list *v1.Node: nodes is forbidden: User \"system:kube-scheduler\" cannot list resource \"nodes\" in API group \"\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.Node"
E1001 05:10:28.932922       1 reflector.go:205] "Failed to watch" err="failed to list *v1.PodDisruptionBudget: poddisruptionbudgets.policy is forbidden: User \"system:kube-scheduler\" cannot list resource \"poddisruptionbudgets\" in API group \"policy\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.PodDisruptionBudget"
E1001 05:10:28.933142       1 reflector.go:205] "Failed to watch" err="failed to list *v1.ReplicationController: replicationcontrollers is forbidden: User \"system:kube-scheduler\" cannot list resource \"replicationcontrollers\" in API group \"\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.ReplicationController"
E1001 05:10:28.933303       1 reflector.go:205] "Failed to watch" err="failed to list *v1.CSINode: csinodes.storage.k8s.io is forbidden: User \"system:kube-scheduler\" cannot list resource \"csinodes\" in API group \"storage.k8s.io\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.CSINode"
E1001 05:10:28.933325       1 reflector.go:205] "Failed to watch" err="failed to list *v1.PersistentVolumeClaim: persistentvolumeclaims is forbidden: User \"system:kube-scheduler\" cannot list resource \"persistentvolumeclaims\" in API group \"\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.PersistentVolumeClaim"
E1001 05:10:28.986351       1 reflector.go:205] "Failed to watch" err="failed to list *v1.PersistentVolume: persistentvolumes is forbidden: User \"system:kube-scheduler\" cannot list resource \"persistentvolumes\" in API group \"\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.PersistentVolume"
E1001 05:10:28.992221       1 reflector.go:205] "Failed to watch" err="failed to list *v1.ResourceSlice: resourceslices.resource.k8s.io is forbidden: User \"system:kube-scheduler\" cannot list resource \"resourceslices\" in API group \"resource.k8s.io\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.ResourceSlice"
E1001 05:10:28.992739       1 reflector.go:205] "Failed to watch" err="failed to list *v1.Namespace: namespaces is forbidden: User \"system:kube-scheduler\" cannot list resource \"namespaces\" in API group \"\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.Namespace"
E1001 05:10:28.993247       1 reflector.go:205] "Failed to watch" err="failed to list *v1.Service: services is forbidden: User \"system:kube-scheduler\" cannot list resource \"services\" in API group \"\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.Service"
E1001 05:10:28.993603       1 reflector.go:205] "Failed to watch" err="failed to list *v1.CSIDriver: csidrivers.storage.k8s.io is forbidden: User \"system:kube-scheduler\" cannot list resource \"csidrivers\" in API group \"storage.k8s.io\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.CSIDriver"
E1001 05:10:28.999851       1 reflector.go:205] "Failed to watch" err="failed to list *v1.ResourceClaim: resourceclaims.resource.k8s.io is forbidden: User \"system:kube-scheduler\" cannot list resource \"resourceclaims\" in API group \"resource.k8s.io\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.ResourceClaim"
E1001 05:10:28.999981       1 reflector.go:205] "Failed to watch" err="failed to list *v1.Pod: pods is forbidden: User \"system:kube-scheduler\" cannot list resource \"pods\" in API group \"\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.Pod"
E1001 05:10:29.745875       1 reflector.go:205] "Failed to watch" err="failed to list *v1.ReplicaSet: replicasets.apps is forbidden: User \"system:kube-scheduler\" cannot list resource \"replicasets\" in API group \"apps\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.ReplicaSet"
E1001 05:10:29.755270       1 reflector.go:205] "Failed to watch" err="failed to list *v1.StatefulSet: statefulsets.apps is forbidden: User \"system:kube-scheduler\" cannot list resource \"statefulsets\" in API group \"apps\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.StatefulSet"
E1001 05:10:29.784581       1 reflector.go:205] "Failed to watch" err="failed to list *v1.VolumeAttachment: volumeattachments.storage.k8s.io is forbidden: User \"system:kube-scheduler\" cannot list resource \"volumeattachments\" in API group \"storage.k8s.io\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.VolumeAttachment"
E1001 05:10:29.808415       1 reflector.go:205] "Failed to watch" err="failed to list *v1.PersistentVolume: persistentvolumes is forbidden: User \"system:kube-scheduler\" cannot list resource \"persistentvolumes\" in API group \"\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.PersistentVolume"
E1001 05:10:29.827124       1 reflector.go:205] "Failed to watch" err="failed to list *v1.Service: services is forbidden: User \"system:kube-scheduler\" cannot list resource \"services\" in API group \"\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.Service"
E1001 05:10:29.981718       1 reflector.go:205] "Failed to watch" err="failed to list *v1.ConfigMap: configmaps \"extension-apiserver-authentication\" is forbidden: User \"system:kube-scheduler\" cannot list resource \"configmaps\" in API group \"\" in the namespace \"kube-system\"" logger="UnhandledError" reflector="runtime/asm_amd64.s:1700" type="*v1.ConfigMap"
E1001 05:10:30.005694       1 reflector.go:205] "Failed to watch" err="failed to list *v1.Namespace: namespaces is forbidden: User \"system:kube-scheduler\" cannot list resource \"namespaces\" in API group \"\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.Namespace"
E1001 05:10:30.062560       1 reflector.go:205] "Failed to watch" err="failed to list *v1.Node: nodes is forbidden: User \"system:kube-scheduler\" cannot list resource \"nodes\" in API group \"\" at the cluster scope" logger="UnhandledError" reflector="k8s.io/client-go/informers/factory.go:160" type="*v1.Node"
I1001 05:10:33.021992       1 shared_informer.go:356] "Caches are synced" controller="client-ca::kube-system::extension-apiserver-authentication::client-ca-file"


==> kubelet <==
Oct 01 05:23:47 minikube kubelet[3060]: I1001 05:23:47.456826    3060 scope.go:117] "RemoveContainer" containerID="eeb2bcd0ae4f280b9e3a0dd834cb029c5ef930087548273a69d63789f1388d3e"
Oct 01 05:23:47 minikube kubelet[3060]: E1001 05:23:47.457058    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 2m40s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:23:50 minikube kubelet[3060]: I1001 05:23:50.456599    3060 scope.go:117] "RemoveContainer" containerID="a6c84843cff24c904ce6ba4d1fef6a35892c340df370807e2b7d5062b10b559d"
Oct 01 05:23:50 minikube kubelet[3060]: E1001 05:23:50.456792    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 2m40s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"
Oct 01 05:23:59 minikube kubelet[3060]: I1001 05:23:59.456764    3060 scope.go:117] "RemoveContainer" containerID="eeb2bcd0ae4f280b9e3a0dd834cb029c5ef930087548273a69d63789f1388d3e"
Oct 01 05:23:59 minikube kubelet[3060]: E1001 05:23:59.456990    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 2m40s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:24:04 minikube kubelet[3060]: I1001 05:24:04.456806    3060 scope.go:117] "RemoveContainer" containerID="a6c84843cff24c904ce6ba4d1fef6a35892c340df370807e2b7d5062b10b559d"
Oct 01 05:24:04 minikube kubelet[3060]: E1001 05:24:04.457045    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 2m40s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"
Oct 01 05:24:14 minikube kubelet[3060]: I1001 05:24:14.453770    3060 scope.go:117] "RemoveContainer" containerID="eeb2bcd0ae4f280b9e3a0dd834cb029c5ef930087548273a69d63789f1388d3e"
Oct 01 05:24:14 minikube kubelet[3060]: E1001 05:24:14.454174    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 2m40s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:24:17 minikube kubelet[3060]: I1001 05:24:17.453968    3060 scope.go:117] "RemoveContainer" containerID="a6c84843cff24c904ce6ba4d1fef6a35892c340df370807e2b7d5062b10b559d"
Oct 01 05:24:17 minikube kubelet[3060]: E1001 05:24:17.454383    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 2m40s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"
Oct 01 05:24:25 minikube kubelet[3060]: I1001 05:24:25.452989    3060 scope.go:117] "RemoveContainer" containerID="eeb2bcd0ae4f280b9e3a0dd834cb029c5ef930087548273a69d63789f1388d3e"
Oct 01 05:24:25 minikube kubelet[3060]: E1001 05:24:25.453187    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 2m40s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:24:30 minikube kubelet[3060]: I1001 05:24:30.453999    3060 scope.go:117] "RemoveContainer" containerID="a6c84843cff24c904ce6ba4d1fef6a35892c340df370807e2b7d5062b10b559d"
Oct 01 05:24:37 minikube kubelet[3060]: I1001 05:24:37.450794    3060 scope.go:117] "RemoveContainer" containerID="eeb2bcd0ae4f280b9e3a0dd834cb029c5ef930087548273a69d63789f1388d3e"
Oct 01 05:24:37 minikube kubelet[3060]: E1001 05:24:37.450994    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 2m40s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:24:37 minikube kubelet[3060]: I1001 05:24:37.682955    3060 scope.go:117] "RemoveContainer" containerID="a6c84843cff24c904ce6ba4d1fef6a35892c340df370807e2b7d5062b10b559d"
Oct 01 05:24:37 minikube kubelet[3060]: I1001 05:24:37.683347    3060 scope.go:117] "RemoveContainer" containerID="c1dcc613411e77cae989b96f9f3927c64965902f2f133d32fe84917060eb676a"
Oct 01 05:24:37 minikube kubelet[3060]: E1001 05:24:37.683773    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"
Oct 01 05:24:48 minikube kubelet[3060]: I1001 05:24:48.451046    3060 scope.go:117] "RemoveContainer" containerID="eeb2bcd0ae4f280b9e3a0dd834cb029c5ef930087548273a69d63789f1388d3e"
Oct 01 05:24:49 minikube kubelet[3060]: I1001 05:24:49.449875    3060 scope.go:117] "RemoveContainer" containerID="c1dcc613411e77cae989b96f9f3927c64965902f2f133d32fe84917060eb676a"
Oct 01 05:24:49 minikube kubelet[3060]: E1001 05:24:49.450893    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"
Oct 01 05:24:54 minikube kubelet[3060]: I1001 05:24:54.060103    3060 scope.go:117] "RemoveContainer" containerID="c1dcc613411e77cae989b96f9f3927c64965902f2f133d32fe84917060eb676a"
Oct 01 05:24:54 minikube kubelet[3060]: E1001 05:24:54.060401    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"
Oct 01 05:24:56 minikube kubelet[3060]: I1001 05:24:56.917764    3060 scope.go:117] "RemoveContainer" containerID="eeb2bcd0ae4f280b9e3a0dd834cb029c5ef930087548273a69d63789f1388d3e"
Oct 01 05:24:56 minikube kubelet[3060]: I1001 05:24:56.918047    3060 scope.go:117] "RemoveContainer" containerID="d8015551a05c14a96b800b4bf6abee39a8f24957e248d73e83841cf131cbde06"
Oct 01 05:24:56 minikube kubelet[3060]: E1001 05:24:56.918208    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:25:06 minikube kubelet[3060]: I1001 05:25:06.447282    3060 scope.go:117] "RemoveContainer" containerID="c1dcc613411e77cae989b96f9f3927c64965902f2f133d32fe84917060eb676a"
Oct 01 05:25:06 minikube kubelet[3060]: E1001 05:25:06.447586    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"
Oct 01 05:25:08 minikube kubelet[3060]: I1001 05:25:08.447300    3060 scope.go:117] "RemoveContainer" containerID="d8015551a05c14a96b800b4bf6abee39a8f24957e248d73e83841cf131cbde06"
Oct 01 05:25:08 minikube kubelet[3060]: E1001 05:25:08.447628    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:25:14 minikube kubelet[3060]: I1001 05:25:14.110581    3060 scope.go:117] "RemoveContainer" containerID="d8015551a05c14a96b800b4bf6abee39a8f24957e248d73e83841cf131cbde06"
Oct 01 05:25:14 minikube kubelet[3060]: E1001 05:25:14.110750    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:25:21 minikube kubelet[3060]: I1001 05:25:21.447674    3060 scope.go:117] "RemoveContainer" containerID="c1dcc613411e77cae989b96f9f3927c64965902f2f133d32fe84917060eb676a"
Oct 01 05:25:21 minikube kubelet[3060]: E1001 05:25:21.448021    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"
Oct 01 05:25:28 minikube kubelet[3060]: I1001 05:25:28.446947    3060 scope.go:117] "RemoveContainer" containerID="d8015551a05c14a96b800b4bf6abee39a8f24957e248d73e83841cf131cbde06"
Oct 01 05:25:28 minikube kubelet[3060]: E1001 05:25:28.447138    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:25:33 minikube kubelet[3060]: I1001 05:25:33.447548    3060 scope.go:117] "RemoveContainer" containerID="c1dcc613411e77cae989b96f9f3927c64965902f2f133d32fe84917060eb676a"
Oct 01 05:25:33 minikube kubelet[3060]: E1001 05:25:33.447900    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"
Oct 01 05:25:42 minikube kubelet[3060]: I1001 05:25:42.444452    3060 scope.go:117] "RemoveContainer" containerID="d8015551a05c14a96b800b4bf6abee39a8f24957e248d73e83841cf131cbde06"
Oct 01 05:25:42 minikube kubelet[3060]: E1001 05:25:42.444783    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:25:47 minikube kubelet[3060]: I1001 05:25:47.443394    3060 scope.go:117] "RemoveContainer" containerID="c1dcc613411e77cae989b96f9f3927c64965902f2f133d32fe84917060eb676a"
Oct 01 05:25:47 minikube kubelet[3060]: E1001 05:25:47.443610    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"
Oct 01 05:25:57 minikube kubelet[3060]: I1001 05:25:57.443957    3060 scope.go:117] "RemoveContainer" containerID="d8015551a05c14a96b800b4bf6abee39a8f24957e248d73e83841cf131cbde06"
Oct 01 05:25:57 minikube kubelet[3060]: E1001 05:25:57.444292    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:25:59 minikube kubelet[3060]: I1001 05:25:59.443825    3060 scope.go:117] "RemoveContainer" containerID="c1dcc613411e77cae989b96f9f3927c64965902f2f133d32fe84917060eb676a"
Oct 01 05:25:59 minikube kubelet[3060]: E1001 05:25:59.444205    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"
Oct 01 05:26:12 minikube kubelet[3060]: I1001 05:26:12.440678    3060 scope.go:117] "RemoveContainer" containerID="d8015551a05c14a96b800b4bf6abee39a8f24957e248d73e83841cf131cbde06"
Oct 01 05:26:12 minikube kubelet[3060]: E1001 05:26:12.441069    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:26:13 minikube kubelet[3060]: I1001 05:26:13.441128    3060 scope.go:117] "RemoveContainer" containerID="c1dcc613411e77cae989b96f9f3927c64965902f2f133d32fe84917060eb676a"
Oct 01 05:26:13 minikube kubelet[3060]: E1001 05:26:13.441392    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"
Oct 01 05:26:24 minikube kubelet[3060]: I1001 05:26:24.441235    3060 scope.go:117] "RemoveContainer" containerID="c1dcc613411e77cae989b96f9f3927c64965902f2f133d32fe84917060eb676a"
Oct 01 05:26:24 minikube kubelet[3060]: E1001 05:26:24.441445    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"
Oct 01 05:26:27 minikube kubelet[3060]: I1001 05:26:27.440926    3060 scope.go:117] "RemoveContainer" containerID="d8015551a05c14a96b800b4bf6abee39a8f24957e248d73e83841cf131cbde06"
Oct 01 05:26:27 minikube kubelet[3060]: E1001 05:26:27.441195    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:26:39 minikube kubelet[3060]: I1001 05:26:39.443845    3060 scope.go:117] "RemoveContainer" containerID="d8015551a05c14a96b800b4bf6abee39a8f24957e248d73e83841cf131cbde06"
Oct 01 05:26:39 minikube kubelet[3060]: E1001 05:26:39.444033    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cp9sx_default(12016bab-bf7c-42bd-bec5-bf40002cc84b)\"" pod="default/kubernetes-demo-api-54db6ffd65-cp9sx" podUID="12016bab-bf7c-42bd-bec5-bf40002cc84b"
Oct 01 05:26:39 minikube kubelet[3060]: I1001 05:26:39.444054    3060 scope.go:117] "RemoveContainer" containerID="c1dcc613411e77cae989b96f9f3927c64965902f2f133d32fe84917060eb676a"
Oct 01 05:26:39 minikube kubelet[3060]: E1001 05:26:39.444193    3060 pod_workers.go:1324] "Error syncing pod, skipping" err="failed to \"StartContainer\" for \"kubernetes-demo-api\" with CrashLoopBackOff: \"back-off 5m0s restarting failed container=kubernetes-demo-api pod=kubernetes-demo-api-54db6ffd65-cjqpf_default(ea21baed-ff60-4641-9aa7-40f31eadba32)\"" pod="default/kubernetes-demo-api-54db6ffd65-cjqpf" podUID="ea21baed-ff60-4641-9aa7-40f31eadba32"


==> storage-provisioner [9a8ce4122766] <==
I1001 05:10:37.464142       1 storage_provisioner.go:116] Initializing the minikube storage provisioner...
F1001 05:10:58.502022       1 main.go:39] error getting server version: Get "https://*********:443/version?timeout=32s": dial tcp *********:443: connect: connection refused


==> storage-provisioner [e8a3ae3e70ac] <==
W1001 05:25:45.310118       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:45.315088       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:47.318784       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:47.324429       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:49.330483       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:49.338629       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:51.343719       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:51.349882       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:53.354814       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:53.362084       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:55.368659       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:55.377867       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:57.383080       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:57.393640       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:59.398751       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:25:59.408996       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:01.412449       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:01.416944       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:03.423348       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:03.431974       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:05.435212       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:05.441250       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:07.446442       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:07.452670       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:09.455637       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:09.458878       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:11.462680       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:11.468300       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:13.472070       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:13.476205       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:15.479224       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:15.484033       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:17.486856       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:17.491938       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:19.495780       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:19.501555       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:21.507543       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:21.515971       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:23.520718       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:23.526683       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:25.532747       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:25.542936       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:27.549312       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:27.555902       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:29.560154       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:29.564159       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:31.568664       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:31.580207       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:33.586098       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:33.593207       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:35.595485       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:35.603857       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:37.609554       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:37.617108       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:39.620027       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:39.624799       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:41.627864       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:41.631174       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:43.634610       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice
W1001 05:26:43.639265       1 warnings.go:70] v1 Endpoints is deprecated in v1.33+; use discovery.k8s.io/v1 EndpointSlice

